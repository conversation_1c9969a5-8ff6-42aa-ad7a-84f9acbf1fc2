.scheme{
    line-height: 30px;
}
.scheme-title {
    max-width: 760px!important;
    padding: 5px 10px;
    font-weight: bold;
    margin: 0 auto;
    font-size: 14px;
}
.scheme-title .time {
    float: right;
    font-weight: normal;
}
.scheme-content {
    padding: 5px 0;
    border: 1px solid #ccc;
    max-width: 960px!important;
    display: block!important;
    position: relative!important;
    line-height: 30px;
    margin:0 auto 30px;
    font-size: 12px;
}
.scheme-content .cat {
    border-top: 1px dotted #ccc;
    display: inline-block;
    vertical-align: top;
    min-width: 450px;
    max-width: 450px;
    padding: 3px 10px;
}
.scheme-content .cat-title {
    font-weight: bold;
}
.scheme-content .project {
    display: inline-table;
    min-width: 275px;
    max-width: 275px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.scheme-content .pay {
    display: inline-table;
    min-width: 65px;
    max-width: 65px;
    margin-right: 10px;
}

body {
    padding: 50px;
    font: 14px "Lucida Grande", Helvetica, Arial, sans-serif;
}


