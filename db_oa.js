var mssql = require('mssql');  
var db = {};  
var config = {  
    user: 'sa',  
    password: 'Dellr430',  
    server: '47.243.121.163',   
    database: 'OA',  
    port:1433,  
    pool: {  
        min: 0,  
        max: 10,  
        idleTimeoutMillis: 3000  
    }  
};  
 
db.sql = function (sql, callBack) {  
    var connection = new mssql.Connection(config, function (err) {  
        if (err) {  
            console.log(err);  
            return;  
        }  
        var ps = new mssql.PreparedStatement(connection);  
        ps.prepare(sql, function (err) {  
            if (err){  
                console.log(err);  
                return;  
            }  
            ps.execute('', function (err, result, affected) {  
                if (err){  
                    console.log(err);  
                    return;  
                }  

                ps.unprepare(function (err) {  
                    if (err){  
                        console.log(err);  
                        callback(err,null);  
                        return;  
                    }  
                    callBack(err, result, affected);  
                });  
            });  
        });  
    });  
};  

module.exports = db;  