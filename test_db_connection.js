/**
 * 数据库连接测试脚本
 * 用于测试修复后的数据库连接池是否能正常处理大量并发查询
 */

const db_oa_new = require('./db_oa_new.js');

async function testDatabaseConnections() {
    console.log('开始数据库连接测试...');
    
    try {
        // 测试1: 基本连接测试
        console.log('\n=== 测试1: 基本连接测试 ===');
        const version = await db_oa_new.getVersion();
        console.log('数据库版本:', version);
        
        // 测试2: 连接池状态检查
        console.log('\n=== 测试2: 连接池状态检查 ===');
        const poolStatus = db_oa_new.getPoolStatus();
        console.log('连接池状态:', poolStatus);
        
        // 测试3: 连续查询测试（模拟您遇到的问题）
        console.log('\n=== 测试3: 连续查询测试 (30次) ===');
        const promises = [];
        
        for (let i = 1; i <= 30; i++) {
            const promise = db_oa_new.query('SELECT ? as test_number, NOW() as current_time', [i])
                .then(result => {
                    console.log(`查询 ${i} 完成:`, result[0]);
                    return { success: true, queryNumber: i };
                })
                .catch(error => {
                    console.error(`查询 ${i} 失败:`, error.message);
                    return { success: false, queryNumber: i, error: error.message };
                });
            
            promises.push(promise);
            
            // 每5个查询后稍微延迟，模拟真实使用场景
            if (i % 5 === 0) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        
        // 等待所有查询完成
        const results = await Promise.all(promises);
        
        // 统计结果
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        console.log(`\n查询结果统计:`);
        console.log(`成功: ${successful}`);
        console.log(`失败: ${failed}`);
        
        if (failed > 0) {
            console.log('失败的查询:');
            results.filter(r => !r.success).forEach(r => {
                console.log(`  查询 ${r.queryNumber}: ${r.error}`);
            });
        }
        
        // 测试4: 最终连接池状态
        console.log('\n=== 测试4: 最终连接池状态 ===');
        const finalPoolStatus = db_oa_new.getPoolStatus();
        console.log('最终连接池状态:', finalPoolStatus);
        
        // 测试5: 并发查询测试
        console.log('\n=== 测试5: 并发查询测试 (20个并发) ===');
        const concurrentPromises = [];
        
        for (let i = 1; i <= 20; i++) {
            const promise = db_oa_new.query('SELECT ? as concurrent_test, SLEEP(0.1) as delay, NOW() as time', [i])
                .then(result => {
                    console.log(`并发查询 ${i} 完成`);
                    return { success: true, queryNumber: i };
                })
                .catch(error => {
                    console.error(`并发查询 ${i} 失败:`, error.message);
                    return { success: false, queryNumber: i, error: error.message };
                });
            
            concurrentPromises.push(promise);
        }
        
        const concurrentResults = await Promise.all(concurrentPromises);
        const concurrentSuccessful = concurrentResults.filter(r => r.success).length;
        const concurrentFailed = concurrentResults.filter(r => !r.success).length;
        
        console.log(`\n并发查询结果统计:`);
        console.log(`成功: ${concurrentSuccessful}`);
        console.log(`失败: ${concurrentFailed}`);
        
        console.log('\n=== 测试完成 ===');
        console.log('如果所有测试都成功，说明数据库连接问题已经修复！');
        
    } catch (error) {
        console.error('测试过程中发生错误:', error);
    }
}

// 运行测试
if (require.main === module) {
    testDatabaseConnections()
        .then(() => {
            console.log('\n测试脚本执行完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('测试脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { testDatabaseConnections };
