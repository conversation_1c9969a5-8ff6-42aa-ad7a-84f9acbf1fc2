const express = require('express');
const router = express.Router();
// const db_oa = require('../db_oa.js');
const fs = require('fs');
const jwt = require('jsonwebtoken')
const jose = require('node-jose');
const crypto = require('crypto');
const axios = require('axios')
const qs = require('qs')
var db = require('../db_te.js');
const db_oa_new = require('../db_oa_new.js');
var bodyParser = require('body-parser');
var jsonParser = bodyParser.json();
var urlencodedParser = bodyParser.urlencoded({ extended: false });

router.use(jsonParser);
router.use(urlencodedParser);

const sign_key = '4200c293fa947a92b739331d9ddc2758'
const generateToken = (employeeNumber) => {
	return jwt.sign(
		{
			employeeNumber
		},
		sign_key,
		{
			algorithm: "HS256",
			issuer: "BE",
			expiresIn: 30,
		}
	)
}

async function getPrivateKey(privateKey) {
	const keystore = jose.JWK.createKeyStore();
	await keystore.add(privateKey, 'pem');
	const key = keystore.get({ kty: 'RSA', use: 'sig' });
	console.log(key)
	return key.toPEM(true);
}

const serviceAccount = {
	"clientId": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a",
	"privateKey": {
		"p": "-ZlBOP445kSFQExfDF6DhIAPUmrJ7XRxCLRSTIH2NLZR42DERM3x2btT2umpA7RfKPYYTNaC0SZTEmS2UlXQ2uUpH32cfmE3_UtHJtJUFU4acx_YkQUq8xGkuZDrgX_c2glDk8nOta1wGe7BupDw7cReNriQE8TcqcjMBPxPQD0",
		"kty": "RSA",
		"q": "wweNeqJ33QUI8iMNN3y89PJCBAGaArbWJSCZysPcuu8VrOUB_SQWF_I3OrLBHszIhrwFK2yemjOrpIdzCNFffvtwABQz3CR_3tErHjAZOZmp8Td4WKaWFZPhncYVadhaNCUsH8ZQBI6i_KVUsLLhSE7NAfUj9LsqTte3_aj-cTk",
		"d": "p96bEkYWczV0VJR07szJFAq8IblYMRo_cfacwwTLnhsNfX57odcReE7reQGJU58kO1BjzAGslB2O1NlEg0XQFiCxeE3h23Ys7ZU7qboZTNgAsrfDdklgfMfJRRrzb4RUnihen9YckEEB1s3HKYvq6kuv27uWxORs6Upnbi8kwLGx90SbYBntavAs13vUPnxyInt45NA36nFsAK4oPKTAjul4rA304GzriZXG04lzrafhftjiyv9pzLiaPXKbfiDgIgZ8d4iU8WuPO-eWTz2fSc9ZkPMbCuYxmCcgKBskzdj50w-Dw5woA8or1EHF8qiZOoQVY0D6IJAaDhonaCXJIQ",
		"e": "AQAB",
		"use": "sig",
		"kid": "755b29f7-ac31-4eec-97c4-d08fcd4d08cc",
		"qi": "elGUyKvJzKaeXMVy-w43L89JCaP5UOII4aL9tUcRiPqyyxVFK-b5pC-rlIZlQaxfLSjCH8Ng8b9gfNGKHO_JGpozy0yiydt7cyVJ5xTwyn3zdgj0Y9ltT8OMLWyvV-aelWeZcxIU564wJBBzyOoZP04W3fVvpP9wzbDJL4vBQuI",
		"dp": "T-MtbQBxVcI4pAvNoDfgAFi8jrR_TPEn_JE6hiJSmyx_qTcfmqYP8pBylm6TGgxxKLsbq_w8gpSxogF6GYA-CNGwB9Q8cnbLZNbjPxmyr97_uDlJJ6Fvzbyn7Hzl49z44xB0AKlm799DMgmpF_2sH23qebk45BruRalvI2leNEE",
		"dq": "X4HtWOcDR-tVTC66JDgj2NhiV8_o-fdLu3bRmah9CdZXiQnFBZCMeUGWplovlMO4V7Lv3SoeKWmwXqwuO109JNRBLui11NLTA-zu7qOjVr_Xp1cAynkZ-osm67q7ddALrImfqBfXy8OGMa9NYBLZnC1q29COwSR_PYkKg2Ak6pE",
		"n": "vicZ0CyRnSSTtJ_PKmsakvGLlo6ZU6akIX7IwYaSP86i4C6Nh6s8wv1QMETk4tkDZ5SilUBwhO1xS9plY9hH8gV52zXM-K-lC3TN68lyrd65ZfFYn2GpOTMHwqyy0m3QF2t-JQ5DOMD2MImL0Cx6qZxADKb2xIzxj5nGXc5k9lByRDW1CJDt7XZbEfjEDyXXJw-Gt0LiamOZ0ktvHG5K8IOVV1bAGGs5sTHv9UpW_sd7DcrOTxQB1hVGoxY_-_cwg8itgnuFWVqbkMwr7xYYYtWPvEcISjVF3ASmbHAscxpeXJC7Jb_1IbMh5hR5GTRvSEjKj_0SzZ6swxCN7YY6lQ"
	}
}

const header = {
	"alg": "RS256",
	"typ": "JWT",
	"kid": "755b29f7-ac31-4eec-97c4-d08fcd4d08cc"
};

let payload = {}

const generateTokenTencent = (payload, privateKey) => {
	return jwt.sign(payload, privateKey, { algorithm: 'RS256', header })
}

function getIdToken(clientSdkId, username, name) {
	try {
		const idSecret = `MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCRIAxTdKB8PTDTYHaAz/Dzf66GLdp5zSs1l5CJ2k/pK+XS6jRyJzydItWovcP63lqWnoQkXNaMNs0cY/5tEIhNJLX2dSxXeITN26oWTZib8DPDua5Q3oG+Cd2Qqgda/+gQmGt7L6wZrDORT6avtoZEaHPeYOeS8KhmGinowx0/mE8mOvdL+575OtcPDrEbwaEfmrOeurzl1YkDMvgLduxLrtrDxvQc144iEnTSNd0ZqGn0B1MPr+x8GHp0A0UL+hqyCb8HLztfI+x+HFQXhbviRdBUIp5Gtv7/ukmDa8Zckr1htm9Ahxjr8bhS3QVGFaaUoU5+8Esz4dG5MmnriwrJAgMBAAECggEAGfmUezN6rgH9xTeUvkV5qPuHbzixy+8u9q6kDihm+SMERZXpwSzIqo9bkpI0i6hUqnTAzglX1UzRtW0oUrkMzFHvx6b9QsKtcnMIpGlLEU4S1ctcKdlJe4wkfgoQuvbmwGGniyrjMM0jhbSt7vFKYuAXLQxHJoOwgGXabnDNwV3qfCyRS5jtllfqzBX62dUvPbIaSJsVIyJh0U5ZTUtJAqsMs9KEUTziUP03SBWySB1Rh+fietWNH4JFoy6jsfcDWsoQbWO19BzGGmdmMBYLxcPkmWG2u99VOlB+UCg87N3FkoPF3XI9+mLnnPyStngwPYOtXaaKJWmmnlOkHGrQaQKBgQDCwdeaGPCG2rlg5dwyxCSwnlyB/u25jyQvNRB1JoZhULXyoFC9EkiVhsAdia3H1Mu0fVRG90nscjrt+5m5t6UcKGhXzOAy0LkbwLRYPrd1iMBnPvEQ0cbamzNWaY4nL+kvsSUlc91GZjTDi9/xGxDlxrrSQAe+LsQvI4ZOgZuRRwKBgQC+wscGmL11+tsBAfCmlyo1A6LZLSY8meDzaXolDqevu8+sJWY8i90RiG6TZybTXQ1ijEZPTHiCFrYb3lN7g+/2VgG1ogPidG4r8KkjcpNDx04Ze7e58aQgKpx/l5sT0tFlLWrTT2Oao2Zmkm5w8UuUYFf/HpEVSt70bTRIG98LbwKBgDHiGGj9zk2cJDMvbNEL8CHUzfj15MnMYgY9XzPCZFT0qUW1zjg/QJwb4YUvSHWQiYR3PWjX2fVX5MQj12H8KawmpWMntJ0PXPpAx3B6z96o4i8PrgsS0A4aBnIxoxliaiWvB8aqAJkRgJ49a2SD/DX+SsejYthRfipcvP/Krp/tAoGAK/R7obR+K3pDDmdhCTHZwt90nbA+mEYoxE2wnGtVDjQrdRIlDZx4svqJ6CMjmh5d4DBMXydSuT78dU10VQiguVU2DaEoXfzQCgXpI0baUgrRnyOXwRvB4ruO1gOb8yRnpXWioNAxNkeiZ84j7pha/vR4MkvbyFk5KH8lRdxON7cCgYBrGI/KaiEBFFIDJI75QXGEo6aR63lnaEIyOgZBQIhZj7Pqm7OnFg278p+3VJXEKxMD2mxE25Nov6GPt+/FaocP4P94Xceyvzbq1ff2cWg0VhSA7zH4Ikw1BMO/zIZQLvDYhJzq1A/WxffciFACneim3nx4fw4BV7FZiqWW7io2Ig==`;
		const privateKey = crypto.createPrivateKey({
			key: Buffer.from(idSecret, 'base64'),
			format: 'der',
			type: 'pkcs8'
		});

		const headerClaims = {
			alg: "RS256",
			typ: "JWT"
		};

		const now = Date.now();
		const expirationTime = now + 24 * 60 * 60 * 1000;

		const token = jwt.sign(
			{
				sub: username,
				iss: clientSdkId,
				name: name,
				exp: Math.floor(expirationTime / 1000),
				iat: Math.floor(now / 1000)
			},
			privateKey,
			{
				algorithm: 'RS256',
				header: headerClaims
			}
		);

		return token;
	} catch (error) {
		console.error(error);
		return null;
	}
}

router.post('/tencentdirect', function (req, res, next) {
	try {
		const body = req.body
		console.log(body)
		if (body.userid.length <= 0 || body.displayname.length <= 0 || body.url.length <= 0 ) {
			return;
		}

		const clientSdkId = "ai-d2fbfabed37f47fdafd9e88d7212ad48";
		const username = body.userid;
		const name = encodeURI(body.displayname); // unicode, in case chinese char
		const url = body.url

		const idToken = getIdToken(clientSdkId, username, name);

		let actionObj = {
			'action': 'join',
			'params': {
				'meeting_url': url,
				'mode': 0
			}
		}
		const base64Str = Buffer.from(JSON.stringify(actionObj)).toString('base64');
		let baseUrl = 'https://meeting2291098842678-idp.id.meeting.qq.com/cidp/custom/ai-d2fbfabed37f47fdafd9e88d7212ad48/ai-a1daaf3edccd49c78f100bca69bebbcb'
		let fullUrl = baseUrl + '?action=' + base64Str + '&' + 'id_token=' + idToken

		res.send(fullUrl)
	} catch (error) {
		console.error(error)
		return null;
	}
	

});


async function getTencentToken () {
	let payload = {
		"aud": "contacts",
		"iss": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a", //填写ServiceAccount对应的clientId
		"account_type": "serviceAccount",
		"iat": Math.floor(Date.now() / 1000),
		"exp": Math.floor((Date.now() + 3600000) / 1000)
	};

	let result = await getPrivateKey(serviceAccount.privateKey)
	token = generateTokenTencent(payload, result)
	return token
}

router.post('/account', async function (req, res, next) {
	const body = req.body
	let username = body.values.username
	let displayName = body.values.displayName
	let phoneNum = body.values.phoneNum
	let primaryMail = body.values.primaryMail
	let deptId = body.values.deptId
	let zohoid = body.values.zohoid
	let name = body.values.name
	let type = body.values.type 


	let token = ''
	// get token
	payload = {
		"aud": "contacts",
		"iss": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a", //填写ServiceAccount对应的clientId
		"account_type": "serviceAccount",
		"iat": Math.floor(Date.now() / 1000),
		"exp": Math.floor((Date.now() + 3600000) / 1000)
	};

	let result = await getPrivateKey(serviceAccount.privateKey)
	token = generateTokenTencent(payload, result)

	const Headers = {
		'Content-Type': 'application/json',
		'Authorization': `Bearer ${token}`
	};

	try {
		let data = JSON.stringify({
			'values': {
				username,
				displayName,
				phoneNum,
				primaryMail,
				deptId,
			}
		})

		let config = {
			method: 'post',
			maxBodyLength: Infinity,
			url: 'https://meeting2291098842678-admin.id.meeting.qq.com/contacts/api/v1/users',
			headers: { 
			  'Content-Type': 'application/json', 
			  'Authorization': 'Bearer ' + token
			},
			data : data
		}

		axios.request(config)
		.then(async (response) => {
			//console.log(JSON.stringify(response.data));
			//console.log('fuck', response.errorMsg)
			if(response.data.id) {

				// add record to db
				let db_status = ''
				let KeyID = ''
				console.log(`insert into tencentaccount (zohoid, userid, type, name) values('${zohoid}', '${response.data.id}', '${type}', '${name}')`)
				await db.query(`insert into tencentaccount (zohoid, userid, type, name) values('${zohoid}', '${response.data.id}', '${type}', '${name}')`).then(function (data) {
					console.log('>>' + JSON.stringify(data) + ' updated');
					if (JSON.stringify(data).indexOf('error_exec') >= 0) {
						console.log('return=> ERROR');
						db_status = 'ERROR';
					} else {
						db_status = 'SUCCESS';
						KeyID = data.insertId;
					}
	
				}).catch(function (err) {
				});

				res.send({'status': 200, 'msg': response.data.id})
			} else {
				res.send('ok')
			}
			
		})
		.catch(function (error) {
			if (error.response) {
				// The request was made and the server responded with a status code
				// that falls out of the range of 2xx
				console.log(error.response.data);
				console.log(error.response.status);
				if (error.response.data.errorCode == '*********')
				{
					res.send('account duplicated')
				} else {
					res.send(error.response.data)
				}
				
				//console.log(error.response.headers);
			} else if (error.request) {
				// The request was made but no response was received
				// `error.request` is an instance of XMLHttpRequest in the browser 
				// and an instance of http.ClientRequest in node.js
				console.log(error.request);
			} else {
				// Something happened in setting up the request that triggered an Error
				console.log('Error', error.message);
			}
			//res.send('err')
		})

	} catch (err) {
		res.send(err.message);
	}

})

router.get('/tencent', async function (req, res, next) {

	payload = {
		"aud": "contacts",
		"iss": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a", //填写ServiceAccount对应的clientId
		"account_type": "serviceAccount",
		"iat": Math.floor(Date.now() / 1000),
		"exp": Math.floor((Date.now() + 3600000) / 1000)
	};

	let actionObj = {
		'action': 'join',
		'params': {
			'meeting_url': 'https://meeting.tencent.com/dm/xmHQdH3aSdse',
			'mode': 0
		}
	}
	let baseUrl = 'https://meeting2291098842678-idp.id.meeting.qq.com/cidp/custom/ai-d2fbfabed37f47fdafd9e88d7212ad48/ai-a1daaf3edccd49c78f100bca69bebbcb?id_token='
	const base64Str = Buffer.from(JSON.stringify(actionObj)).toString('base64');

	let result = await getPrivateKey(serviceAccount.privateKey)
	let tokenTencent = generateTokenTencent(payload, result)
	res.send(tokenTencent)

});

// router.get('/', function (req, res, next) {
// 	if (!req.query.employeeid || req.query.employeeid.length < 0) {
// 		res.send('need employeeid')
// 		return
// 	}

// 	let employeeNumber = ''
// 	let employeeID = req.query.employeeid
// 	//let sql = "SELECT EXT_ATTR_2 as email FROM ORG_MEMBER where id = '" + employeeID + "'";
// 	let sql = "SELECT CODE as code FROM ORG_MEMBER where id = '" + employeeID + "'";
// 	db_oa.sql(sql, function (err, result) {
// 		if (err) {
// 			console.log(err);
// 			return;
// 		}
// 		if (result.length > 0) {
// 			console.log(result);
// 			employeeNumber = result[0].code
// 			let token = generateToken(employeeNumber)
// 			let new_url = `https://be.peoplus.cn/peoplusPro/api/sso/jwtlogin?appkey=&redirect=https%3A//be.peoplus.cn&token=${token}`
// 			let url = `https://betest.peoplus.cn/peoplusPro/api/sso/jwtlogin?appkey=&redirect=https%3A//betest.peoplus.cn&token=${token}`
// 			res.redirect(new_url)
// 		} else {
// 			res.send('no record found.');
// 		}
// 	});
// });

router.post('/new', function (req, res, next) {
	try {
		const body = req.body;
		if (!body.employeeid || body.employeeid.length <= 0) {
			res.send('need employeeid')
			return
		}

		let employeeNumber = ''
		let employeeID = fromBase64(body.employeeid);
		//let employeeID = "100001700000000025";
		//let sql = "SELECT EXT_ATTR_2 as email FROM ORG_MEMBER where id = '" + employeeID + "'";
		let sql = "SELECT ID as id, JOB_NUM as code FROM employee where ID = '" + employeeID + "'";
		console.log('chris',employeeID);
		db_oa_new.query(sql, function (err, result) {
			if (err) {
				console.log(err);
				return;
			}
			if (result.length > 0) {
				console.log(result);
				employeeNumber = result[0].code
				let token = generateToken(employeeNumber)
				let new_url = `https://be.peoplus.cn/peoplusPro/api/sso/jwtlogin?appkey=&redirect=https%3A//be.peoplus.cn&token=${token}`
				let url = `https://betest.peoplus.cn/peoplusPro/api/sso/jwtlogin?appkey=&redirect=https%3A//betest.peoplus.cn&token=${token}`
				
				res.send(new_url)
			} else {
				res.send('no record found.');
			}
			console.log('sent')
		});
	} catch (error) {
		console.error(error)
		return null;
	}
});

function fromBase64(base64) {
  try {
    const binaryString = atob(base64);
    const bytes = Uint8Array.from(binaryString, char => char.charCodeAt(0));
    const decoder = new TextDecoder(); // 默认 UTF-8
    return decoder.decode(bytes);
  } catch (e) {
    console.error("Failed to decode Base64:", e);
    return null;
  }
}

module.exports = router;