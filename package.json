{"name": "beconnect-api", "version": "1.0.0", "private": true, "scripts": {"start": "node ./bin/www"}, "dependencies": {"@alicloud/sms-sdk": "^1.1.6", "axios": "^0.24.0", "body-parser": "~1.15.2", "circular-json": "^0.5.9", "cookie-parser": "~1.4.3", "debug": "~2.2.0", "ejs": "~2.5.2", "express": "~4.14.0", "fetch": "^1.1.0", "jade": "~1.11.0", "jose": "^5.2.4", "js-base64": "^3.7.2", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "moment": "^2.29.4", "morgan": "~1.7.0", "mssql": "^3.3.0", "multer": "^1.3.0", "mysql": "^2.18.1", "node-jose": "^2.2.0", "node-schedule": "^1.3.2", "nodemailer": "^6.1.1", "nodemon": "^2.0.14", "pa11y": "^6.2.3", "pnpm": "^10.8.1", "pump": "^3.0.2", "puppeteer": "13.5.2", "qs": "^6.2.0", "serve-favicon": "~2.3.0", "socket.io": "^4.1.3", "swagger-jsdoc": "^6.1.0", "swagger-ui-express": "^4.1.6"}}