[{"name": "Payroll", "sql": "SELECT CASE WHEN Entity = 'SHBE' AND bu_name LIKE 'BJ-%' THEN 'SHBE-BJ' WHEN Entity = 'SHBE' AND bu_name LIKE 'CD-%' THEN 'SHBE-CD' ELSE Entity END AS CalScheme, Entity, FEMail, fnumber, English_Name, Chinese_Name, local, income_year, income_month, Contract_Salary合同工资,Performance_Salary,Commission业绩奖金,Bonus奖金,Allowance补贴,Position_Subsidy岗位津贴, Personal_Leave_Deduction事假扣款, Sickleave_Deduction病假扣款,  Pension_Individual养老金个人, Medical_Individual医保个人, Unemployment_Individual失业金个人, Housing_Fund_Individual公积金个人,Before_Tax_Deduction税前扣款, Children_Education子女教育, Diploma_Education继续教育, Medical_Treatment_for_Serious_Illness大病医疗, Housing_Loan住房贷款, Housing_Rent住房租金, Support_For_Elderly赡养老人, Net_Income当月净收入, Tax税额, Red_Pack, After_Tax_Adjustment税后调整项,Taxable_Salary当月应纳税所得额,  Service_Year, Fullname FROM ( SELECT T_HR_SCalScheme.FName_l2 AS CalScheme, T_BD_Person.FEMail, T_ORG_Admin.FName_L2 AS Entity, T_HR_SCmpCalTable.fnumber, T_BD_Person.FName_L2 AS English_Name, T_BD_Person.CFZwm AS Chinese_Name , CT_HR_Expate.FName_l2 AS local, T_HR_SCmpCalTable.S93 AS Net_Income当月净收入, T_HR_SCmpCalTable.FPeriodYear AS income_year, T_HR_SCmpCalTable.FPeriodMonth AS income_month,  T_HR_SCmpCalTable.S61 AS Contract_Salary合同工资,isnull(T_HR_SCmpCalTable.S170,0) AS Performance_Salary, isnull(T_HR_SCmpCalTable.S89,0) + isnull(T_HR_SCmpCalTable.S86,0) + isnull(T_HR_SCmpCalTable.S84,0) + isnull(T_HR_SCmpCalTable.S63,0) AS Bonus奖金, isnull(T_HR_SCmpCalTable.S64,0) AS Commission业绩奖金, T_HR_SCmpCalTable.S99 AS Service_Year, round(isnull(T_HR_SCmpCalTable.S91, 0) + isnull(T_HR_SCmpCalTable.S120, 0) * isnull(T_HR_SCmpCalTable.S121, 0) + isnull(T_HR_SCmpCalTable.S136, 0) * isnull(T_HR_SCmpCalTable.S137, 0) + isnull(T_HR_SCmpCalTable.S139, 0) * isnull(T_HR_SCmpCalTable.S140, 0), 2) AS Service_Salary, T_HR_SCmpCalTable.S110 AS Red_Pack, T_HR_SCmpCalTable.S87 AS Personal_Leave_Deduction事假扣款,  T_HR_SCmpCalTable.S88 AS Sickleave_Deduction病假扣款, T_HR_SCmpCalTable.S117 AS Before_Tax_Deduction税前扣款 , isnull(T_HR_SCmpCalTable.S68, 0) + isnull(T_HR_SCmpCalTable.S111, 0) + isnull(T_HR_SCmpCalTable.S116, 0) AS Allowance补贴 , T_HR_SCmpCalTable.S62 AS Position_Subsidy岗位津贴, T_HR_SCmpCalTable.S75 AS Pension_Individual养老金个人, T_HR_SCmpCalTable.S77 AS Medical_Individual医保个人, T_HR_SCmpCalTable.S79 AS Unemployment_Individual失业金个人, T_HR_SCmpCalTable.S82 AS Housing_Fund_Individual公积金个人,  CASE WHEN T_HR_SCmpCalTable.FCalSchemeID IN ('bw3F7dijTtWxDJvtyzrI+//N8rE=','JmiTZX9LRHq/P+IATOWaa//N8rE=','B2AKioNaQ3Supl8HoSAx3P/N8rE=') THEN isnull(T_HR_SCmpCalTable.S109, 0) + isnull(T_HR_SCmpCalTable.S112, 0) + isnull(T_HR_SCmpCalTable.S94, 0) ELSE isnull(T_HR_SCmpCalTable.S160, 0) END AS Tax税额 , isnull(T_HR_SCmpCalTable.S119, 0) AS After_Tax_Adjustment税后调整项,isnull(T_HR_SCmpCalTable.S5, 0) AS Taxable_Salary当月应纳税所得额, CT_HR_BU.FName_l2 AS bu_name, T_HR_SCmpCalTable.FBeginDate AS pdate , T_HR_SCmpCalTable.FName_l2 AS Fullname, T_HR_SCmpCalTable.S146 AS Children_Education子女教育, T_HR_SCmpCalTable.S147 AS Diploma_Education继续教育, T_HR_SCmpCalTable.S148 AS Medical_Treatment_for_Serious_Illness大病医疗, T_HR_SCmpCalTable.S149 AS Housing_Loan住房贷款 , T_HR_SCmpCalTable.S150 AS Housing_Rent住房租金, T_HR_SCmpCalTable.S151 AS Support_For_Elderly赡养老人 FROM T_HR_SCmpCalTable LEFT JOIN T_BD_Person ON T_HR_SCmpCalTable.FPersonID = T_BD_Person.FID LEFT JOIN CT_HR_BU ON T_BD_Person.CFBuID = CT_HR_BU.FID LEFT JOIN CT_HR_Expate ON T_BD_Person.CFExpateID = CT_HR_Expate.FID LEFT JOIN T_ORG_Admin ON T_ORG_Admin.FID = T_HR_SCmpCalTable.FCmpCalOrgID LEFT JOIN t_hr_sCmpEmpAccount ON t_hr_sCmpEmpAccount.FPersonID = T_HR_SCmpCalTable.FPersonID left join T_HR_SCalScheme on T_HR_SCmpCalTable.FCalSchemeID = T_HR_SCalScheme.FID WHERE (T_HR_SCmpCalTable.FCalState = '9' OR T_HR_SCmpCalTable.FCalState = '12' OR T_HR_SCmpCalTable.FCalState = '20') AND UPPER(FEMail) = UPPER('p_id') ) a ORDER BY pdate DESC"}]