{"name": "beconnect-api", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "beconnect-api", "version": "1.0.0", "dependencies": {"@alicloud/sms-sdk": "^1.1.6", "axios": "^0.24.0", "body-parser": "~1.15.2", "circular-json": "^0.5.9", "cookie-parser": "~1.4.3", "debug": "~2.2.0", "ejs": "~2.5.2", "express": "~4.14.0", "fetch": "^1.1.0", "jade": "~1.11.0", "jose": "^5.2.4", "js-base64": "^3.7.2", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "moment": "^2.29.4", "morgan": "~1.7.0", "mssql": "^3.3.0", "multer": "^1.3.0", "mysql": "^2.18.1", "node-jose": "^2.2.0", "node-schedule": "^1.3.2", "nodemailer": "^6.1.1", "nodemon": "^2.0.14", "pa11y": "^6.2.3", "pnpm": "^10.8.1", "pump": "^3.0.2", "puppeteer": "13.5.2", "qs": "^6.2.0", "serve-favicon": "~2.3.0", "socket.io": "^4.1.3", "swagger-jsdoc": "^6.1.0", "swagger-ui-express": "^4.1.6"}}, "node_modules/@alicloud/dybaseapi": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/@alicloud/dybaseapi/-/dybaseapi-1.0.0.tgz", "integrity": "sha512-4KEDgqES7IL60T/TWGSkLQkN6BYeHEw8smnPgac2J2MTES5v1ieCWvbLSuUUWi5SAXi7cydKoD4HUOmX7xwM+A==", "dependencies": {"@alicloud/pop-core": "^1.3.3"}}, "node_modules/@alicloud/dysmsapi-2017-05-25": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/@alicloud/dysmsapi-2017-05-25/-/dysmsapi-2017-05-25-1.0.1.tgz", "integrity": "sha512-CVX/Yl9ntwkAOHAZa3j2Y/Io+SlbETw/ILv4UPIDCEIQm/N0EEcN2OXF8cWCUvHeu7OgnFWZKhdJY5GsbjWHDA==", "dependencies": {"@alicloud/pop-core": "^1.5.1"}}, "node_modules/@alicloud/mns": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/@alicloud/mns/-/mns-1.0.0.tgz", "integrity": "sha512-SUJYM8+x3iPy6thIy5Wa/5lw7+RodeDDIPcI3QQ3bZPNCSq+yEC3jMtfVlS3vxGPq+dGjAysI9RGeM7NByeHlw==", "dependencies": {"debug": "^2.6.3", "httpx": "^2.1.1", "kitx": "^1.2.0", "xml2js": "^0.4.17"}}, "node_modules/@alicloud/mns/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/@alicloud/mns/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/@alicloud/pop-core": {"version": "1.7.12", "resolved": "https://registry.npmmirror.com/@alicloud/pop-core/-/pop-core-1.7.12.tgz", "integrity": "sha512-02w3IpR8NPyjGwlDeYbFhG26HyIeUhC8/SJ1rz3DHLLQ4ktvXmw86BBIa+TgnyX/+/98/iaQpCzrfIRZNNYHwA==", "dependencies": {"debug": "^3.1.0", "httpx": "^2.1.2", "json-bigint": "^1.0.0", "kitx": "^1.2.1", "xml2js": "^0.4.17"}}, "node_modules/@alicloud/pop-core/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@alicloud/pop-core/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/@alicloud/sms-sdk": {"version": "1.1.6", "resolved": "https://registry.npmmirror.com/@alicloud/sms-sdk/-/sms-sdk-1.1.6.tgz", "integrity": "sha512-jW5ROjpAtOrNFkZSXb8geJWzkgJ9ggnPx+ve+72eatu78/vad2emdZjRx43/xPqqhA9djRoU3xHQt2xFODiaMQ==", "dependencies": {"@alicloud/dybaseapi": "^1.0.0", "@alicloud/dysmsapi-2017-05-25": "^1.0.1", "@alicloud/mns": "^1.0.0-beta6", "babel-runtime": "^6.26.0"}}, "node_modules/@alicloud/sms-sdk/node_modules/babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmmirror.com/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==", "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "node_modules/@alicloud/sms-sdk/node_modules/core-js": {"version": "2.6.12", "resolved": "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz", "integrity": "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "hasInstallScript": true}, "node_modules/@apidevtools/json-schema-ref-parser": {"version": "9.0.9", "resolved": "https://registry.npmjs.org/@apidevtools/json-schema-ref-parser/-/json-schema-ref-parser-9.0.9.tgz", "integrity": "sha512-GBD2Le9w2+lVFoc4vswGI/TjkNIZSVp7+9xPf+X3uidBfWnAeUWmquteSyt0+VCrhNMWj/FTABISQrD3Z/YA+w==", "dependencies": {"@jsdevtools/ono": "^7.1.3", "@types/json-schema": "^7.0.6", "call-me-maybe": "^1.0.1", "js-yaml": "^4.1.0"}}, "node_modules/@apidevtools/openapi-schemas": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@apidevtools/openapi-schemas/-/openapi-schemas-2.1.0.tgz", "integrity": "sha512-Zc1AlqrJlX3SlpupFGpiLi2EbteyP7fXmUOGup6/DnkRgjP9bgMM/ag+n91rsv0U1Gpz0H3VILA/o3bW7Ua6BQ==", "engines": {"node": ">=10"}}, "node_modules/@apidevtools/swagger-methods": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/@apidevtools/swagger-methods/-/swagger-methods-3.0.2.tgz", "integrity": "sha512-QAkD5kK2b1WfjDS/UQn/qQkbwF31uqRjPTrsCs5ZG9BQGAkjwvqGFjjPqAuzac/IYzpPtRzjCP1WrTuAIjMrXg=="}, "node_modules/@apidevtools/swagger-parser": {"version": "10.0.2", "resolved": "https://registry.npmjs.org/@apidevtools/swagger-parser/-/swagger-parser-10.0.2.tgz", "integrity": "sha512-JFxcEyp8RlNHgBCE98nwuTkZT6eNFPc1aosWV6wPcQph72TSEEu1k3baJD4/x1qznU+JiDdz8F5pTwabZh+Dhg==", "dependencies": {"@apidevtools/json-schema-ref-parser": "^9.0.6", "@apidevtools/openapi-schemas": "^2.0.4", "@apidevtools/swagger-methods": "^3.0.2", "@jsdevtools/ono": "^7.1.3", "call-me-maybe": "^1.0.1", "z-schema": "^4.2.3"}, "peerDependencies": {"openapi-types": ">=7"}}, "node_modules/@jsdevtools/ono": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/@jsdevtools/ono/-/ono-7.1.3.tgz", "integrity": "sha512-4JQNk+************************************+c456qOrbbM/5xcR8huNCCcbVt7+UmizG6GuUvPvKUYg=="}, "node_modules/@sindresorhus/is": {"version": "0.14.0", "resolved": "https://registry.nlark.com/@sindresorhus/is/download/@sindresorhus/is-0.14.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40sindresorhus%2Fis%2Fdownload%2F%40sindresorhus%2Fis-0.14.0.tgz", "integrity": "sha1-n7OjzzEyMoFR81PeRjLgHlIQK+o=", "engines": {"node": ">=6"}}, "node_modules/@szmarczak/http-timer": {"version": "1.1.2", "resolved": "https://registry.nlark.com/@szmarczak/http-timer/download/@szmarczak/http-timer-1.1.2.tgz", "integrity": "sha1-sWZeLEYaLNkvTBu/UNVFTeDUtCE=", "dependencies": {"defer-to-connect": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/@types/component-emitter": {"version": "1.2.10", "resolved": "https://registry.npmjs.org/@types/component-emitter/-/component-emitter-1.2.10.tgz", "integrity": "sha512-bsjleuRKWmGqajMerkzox19aGbscQX5rmmvvXl3wlIp5gMG1HgkiwPxsN5p070fBDKTNSPgojVbuY1+HWMbFhg=="}, "node_modules/@types/cookie": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/@types/cookie/-/cookie-0.4.1.tgz", "integrity": "sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q=="}, "node_modules/@types/cors": {"version": "2.8.12", "resolved": "https://registry.npmjs.org/@types/cors/-/cors-2.8.12.tgz", "integrity": "sha512-vt+kDhq/M2ayberEtJcIN/hxXy1Pk+59g2FV/ZQceeaTyCtCucjL2Q7FXlFjtWn4n15KCr1NE2lNNFhp0lEThw=="}, "node_modules/@types/json-schema": {"version": "7.0.9", "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz", "integrity": "sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ=="}, "node_modules/@types/node": {"version": "16.4.13", "resolved": "https://registry.npmjs.org/@types/node/-/node-16.4.13.tgz", "integrity": "sha512-bLL69sKtd25w7p1nvg9pigE4gtKVpGTPojBFLMkGHXuUgap2sLqQt2qUnqmVCDfzGUL0DRNZP+1prIZJbMeAXg=="}, "node_modules/@types/yauzl": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.0.tgz", "integrity": "sha512-Cn6WYCm0tXv8p6k+A8PvbDG763EDpBoTzHdA+Q/MF6H3sapGjCm9NzoaJncJS9tUKSuCoDs9XHxYYsQDgxR6kw==", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "https://registry.nlark.com/abbrev/download/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg="}, "node_modules/accepts": {"version": "1.3.3", "resolved": "http://registry.npm.taobao.org/accepts/download/accepts-1.3.3.tgz", "integrity": "sha1-w8p0NJOGSMPg2cHjKN1otiLChMo=", "dependencies": {"mime-types": "~2.1.11", "negotiator": "0.6.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "2.7.0", "resolved": "http://registry.npm.taobao.org/acorn/download/acorn-2.7.0.tgz", "integrity": "sha1-q259nYhqrKiwhbwzEreaGYQz8Oc=", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-globals": {"version": "1.0.9", "resolved": "http://registry.npm.taobao.org/acorn-globals/download/acorn-globals-1.0.9.tgz", "integrity": "sha1-VbtemGkVB7dFedBRNBMhfDgMVM8=", "dependencies": {"acorn": "^2.1.0"}}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agent-base/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/agent-base/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/align-text": {"version": "0.1.4", "resolved": "http://registry.npm.taobao.org/align-text/download/align-text-0.1.4.tgz", "integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "dependencies": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/amdefine": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/amdefine/download/amdefine-1.0.1.tgz", "integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "engines": {"node": ">=0.4.2"}}, "node_modules/ansi-align": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/ansi-align/download/ansi-align-3.0.1.tgz?cache=0&sync_timestamp=1632743673432&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-align%2Fdownload%2Fansi-align-3.0.1.tgz", "integrity": "sha1-DN8S4RGs53OobpofrRIlxDyxmlk=", "dependencies": {"string-width": "^4.1.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.nlark.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.2", "resolved": "https://registry.nlark.com/anymatch/download/anymatch-3.1.2.tgz", "integrity": "sha1-wFV8CWrzLxBhmPT04qODU343hxY=", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/append-field": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "integrity": "sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY="}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "node_modules/asap": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/asap/download/asap-1.0.0.tgz", "integrity": "sha1-sqRdpf36ILBJb8N2jMJ8EvqRan0="}, "node_modules/axe-core": {"version": "4.2.4", "resolved": "https://registry.npmjs.org/axe-core/-/axe-core-4.2.4.tgz", "integrity": "sha512-9AiDKFKUCWEQm1Kj4lcq7KFavLqSXdf2m/zJo+NVh4VXlW5iwXRJ6alkKmipCyYorsRnqsICH9XLubP1jBF+Og==", "engines": {"node": ">=4"}}, "node_modules/axios": {"version": "0.24.0", "resolved": "https://registry.npmmirror.com/axios/download/axios-0.24.0.tgz?cache=0&sync_timestamp=1635213960429&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Faxios%2Fdownload%2Faxios-0.24.0.tgz", "integrity": "sha1-gE5voeS5xSiFAd2d/1anoJQNINY=", "dependencies": {"follow-redirects": "^1.14.4"}}, "node_modules/babel-runtime": {"version": "5.8.38", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-5.8.38.tgz", "integrity": "sha1-HAsC62MxL18If/IEUIJ7QlydTBk=", "dependencies": {"core-js": "^1.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="}, "node_modules/base64-arraybuffer": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.4.tgz", "integrity": "sha1-mBjHngWbE1X5fgQooBfIOOkLqBI=", "engines": {"node": ">= 0.6.0"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/base64id": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz", "integrity": "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==", "engines": {"node": "^4.5.0 || >= 5.9"}}, "node_modules/base64url": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/base64url/-/base64url-3.0.1.tgz", "integrity": "sha512-ir1UPr3dkwexU7FdV8qBBbNDRUhMmIekYMFZfi+C/sLNnRESKPl23nB9b2pltqfOQNnGzsDdId90AEtG5tCx4A==", "engines": {"node": ">=6.0.0"}}, "node_modules/basic-auth": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/basic-auth/download/basic-auth-1.0.4.tgz", "integrity": "sha1-Awk1sB3nyblKgksp8/zLdQ06UpA=", "engines": {"node": ">= 0.6"}}, "node_modules/bfj": {"version": "7.0.2", "resolved": "https://registry.npmjs.org/bfj/-/bfj-7.0.2.tgz", "integrity": "sha512-+e/UqUzwmzJamNF50tBV6tZPTORow7gQ96iFow+8b562OdMpEK0BcJEq2OSPEDmAbSMBQ7PKZ87ubFkgxpYWgw==", "dependencies": {"bluebird": "^3.5.5", "check-types": "^11.1.1", "hoopy": "^0.1.4", "tryer": "^1.0.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/big-number": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/big-number/-/big-number-0.3.1.tgz", "integrity": "sha1-rHMCDApZu3nrF8LOLbd/d9l04BM=", "engines": {"node": ">= 0.4.x"}}, "node_modules/bignumber.js": {"version": "9.0.0", "resolved": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.0.0.tgz", "integrity": "sha512-t/OYhhJ2SD+YGBQcjY8GzzDHEk9f3nerxjtfa6tlMXfe7frs/WozhvCNoGvpM0P3bNf3Gq5ZRMlGr5f3r4/N8A==", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.2.0", "resolved": "https://registry.nlark.com/binary-extensions/download/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "engines": {"node": ">=8"}}, "node_modules/biskviit": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/biskviit/-/biskviit-1.0.1.tgz", "integrity": "sha512-VGCXdHbdbpEkFgtjkeoBN8vRlbj1ZRX2/mxhE8asCCRalUx2nBzOomLJv8Aw/nRt5+ccDb+tPKidg4XxcfGW4w==", "dependencies": {"psl": "^1.1.7"}, "engines": {"node": ">=1.0.0"}}, "node_modules/bl": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/bl/-/bl-1.2.3.tgz", "integrity": "sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww==", "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "node_modules/bl/node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "node_modules/bl/node_modules/readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/bl/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="}, "node_modules/body-parser": {"version": "1.15.2", "resolved": "http://registry.npm.taobao.org/body-parser/download/body-parser-1.15.2.tgz", "integrity": "sha1-11eM9PHRHV9uqATO813Hp/9trmc=", "dependencies": {"bytes": "2.4.0", "content-type": "~1.0.2", "debug": "~2.2.0", "depd": "~1.1.0", "http-errors": "~1.5.0", "iconv-lite": "0.4.13", "on-finished": "~2.3.0", "qs": "6.2.0", "raw-body": "~2.1.7", "type-is": "~1.6.13"}, "engines": {"node": ">= 0.8"}}, "node_modules/boxen": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/boxen/download/boxen-5.1.2.tgz?cache=0&sync_timestamp=1634028659618&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fboxen%2Fdownload%2Fboxen-5.1.2.tgz", "integrity": "sha1-eIy2hvyDwfSG36ikDGj8K4MdK1A=", "dependencies": {"ansi-align": "^3.0.0", "camelcase": "^6.2.0", "chalk": "^4.1.0", "cli-boxes": "^2.2.1", "string-width": "^4.2.2", "type-fest": "^0.20.2", "widest-line": "^3.1.0", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/boxen/node_modules/camelcase": {"version": "6.2.0", "resolved": "https://registry.nlark.com/camelcase/download/camelcase-6.2.0.tgz", "integrity": "sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "resolved": "https://registry.nlark.com/braces/download/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/buffer": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "engines": {"node": "*"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA=="}, "node_modules/buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A=="}, "node_modules/buffer-shims": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/buffer-shims/download/buffer-shims-1.0.0.tgz", "integrity": "sha1-mXjOMXOIxkmth5MCjDR37wRKi1E="}, "node_modules/busboy": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/busboy/-/busboy-0.2.14.tgz", "integrity": "sha1-bCpiLvz0fFe7vh4qnDetNseSVFM=", "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/busboy/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "node_modules/busboy/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/bytes": {"version": "2.4.0", "resolved": "http://registry.npm.taobao.org/bytes/download/bytes-2.4.0.tgz", "integrity": "sha1-fZcZb51br39pNeJZhVSe3SpsIzk="}, "node_modules/cacheable-request": {"version": "6.1.0", "resolved": "https://registry.nlark.com/cacheable-request/download/cacheable-request-6.1.0.tgz?cache=0&sync_timestamp=1623237504263&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcacheable-request%2Fdownload%2Fcacheable-request-6.1.0.tgz", "integrity": "sha1-IP+4vRYrpL4R6VZ9gj22UQUsqRI=", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "5.2.0", "resolved": "https://registry.nlark.com/get-stream/download/get-stream-5.2.0.tgz", "integrity": "sha1-SWaheV7lrOZecGxLe+txJX1uItM=", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz?cache=0&sync_timestamp=1634551715073&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-2.0.0.tgz", "integrity": "sha1-JgPni3tLAAbLyi+8yKMgJVislHk=", "engines": {"node": ">=8"}}, "node_modules/call-me-maybe": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/call-me-maybe/-/call-me-maybe-1.0.1.tgz", "integrity": "sha1-JtII6onje1y95gJQoV8DHBak1ms="}, "node_modules/camelcase": {"version": "1.2.1", "resolved": "http://registry.npm.taobao.org/camelcase/download/camelcase-1.2.1.tgz", "integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=", "engines": {"node": ">=0.10.0"}}, "node_modules/center-align": {"version": "0.1.3", "resolved": "http://registry.npm.taobao.org/center-align/download/center-align-0.1.3.tgz", "integrity": "sha1-qg0yYptu6XIgBBHL1EYckHvCt60=", "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.nlark.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646697260&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.nlark.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "engines": {"node": ">=8"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.nlark.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1626703342506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/character-parser": {"version": "1.2.1", "resolved": "http://registry.npm.taobao.org/character-parser/download/character-parser-1.2.1.tgz", "integrity": "sha1-wN3kqxgnE7kZuXCVmhI+zBow/NY="}, "node_modules/check-types": {"version": "11.1.2", "resolved": "https://registry.npmjs.org/check-types/-/check-types-11.1.2.tgz", "integrity": "sha512-tzWzvgePgLORb9/3a0YenggReLKAIb2owL03H2Xdoe5pKcUyWRSEQ8xfCar8t2SIAuEDwtmx2da1YB52YuHQMQ=="}, "node_modules/chokidar": {"version": "3.5.2", "resolved": "https://registry.nlark.com/chokidar/download/chokidar-3.5.2.tgz?cache=0&sync_timestamp=1623763535523&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchokidar%2Fdownload%2Fchokidar-3.5.2.tgz", "integrity": "sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chownr": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz", "integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="}, "node_modules/ci-info": {"version": "2.0.0", "resolved": "https://registry.nlark.com/ci-info/download/ci-info-2.0.0.tgz", "integrity": "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y="}, "node_modules/circular-json": {"version": "0.5.9", "resolved": "https://registry.npmjs.org/circular-json/-/circular-json-0.5.9.tgz", "integrity": "sha512-4ivwqHpIFJZBuhN3g/pEcdbnGUywkBblloGbkglyloVjjR3uT6tieI89MVOfbP2tHX5sgb01FuLgAOzebNlJNQ==", "deprecated": "CircularJSON is in maintenance only, flatted is its successor."}, "node_modules/clean-css": {"version": "3.4.21", "resolved": "http://registry.npm.taobao.org/clean-css/download/clean-css-3.4.21.tgz", "integrity": "sha1-IQHV29GdY9vBanXr1XDnwzlI9ls=", "dependencies": {"commander": "2.8.x", "source-map": "0.4.x"}, "bin": {"cleancss": "bin/cleancss"}, "engines": {"node": ">=0.10.0"}}, "node_modules/clean-css/node_modules/commander": {"version": "2.8.1", "resolved": "http://registry.npm.taobao.org/commander/download/commander-2.8.1.tgz", "integrity": "sha1-Br42f+v9oMMwqh4qBy09yXYkJdQ=", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "engines": {"node": ">= 0.6.x"}}, "node_modules/cli-boxes": {"version": "2.2.1", "resolved": "https://registry.nlark.com/cli-boxes/download/cli-boxes-2.2.1.tgz", "integrity": "sha1-3dUDXSUJT84iDpyrQKRYQKRAMY8=", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cliui": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/cliui/download/cliui-2.1.0.tgz", "integrity": "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=", "dependencies": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}}, "node_modules/cliui/node_modules/wordwrap": {"version": "0.0.2", "resolved": "http://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.2.tgz", "integrity": "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=", "engines": {"node": ">=0.4.0"}}, "node_modules/clone-response": {"version": "1.0.2", "resolved": "https://registry.nlark.com/clone-response/download/clone-response-1.0.2.tgz", "integrity": "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=", "dependencies": {"mimic-response": "^1.0.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npm.taobao.org/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.nlark.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="}, "node_modules/commander": {"version": "2.6.0", "resolved": "http://registry.npm.taobao.org/commander/download/commander-2.6.0.tgz", "integrity": "sha1-nfflL7Kgyw+4kFjugMMQQiXzfh0=", "engines": {"node": ">= 0.6.x"}}, "node_modules/component-emitter": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.0.tgz", "integrity": "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "engines": ["node >= 0.8"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/configstore": {"version": "5.0.1", "resolved": "https://registry.nlark.com/configstore/download/configstore-5.0.1.tgz", "integrity": "sha1-02UCG130uYzdGH1qOw4/anzF7ZY=", "dependencies": {"dot-prop": "^5.2.0", "graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "unique-string": "^2.0.0", "write-file-atomic": "^3.0.0", "xdg-basedir": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/constantinople": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/constantinople/download/constantinople-3.0.2.tgz", "integrity": "sha1-S5RdmTeQe82Y7ldRIsOBdRZUQUE=", "deprecated": "Please update to at least constantinople 3.1.1", "dependencies": {"acorn": "^2.1.0"}}, "node_modules/content-disposition": {"version": "0.5.1", "resolved": "http://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.1.tgz", "integrity": "sha1-h0dsamfI2qh+Muh2Ft+IO6f7Bxs=", "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/content-type/download/content-type-1.0.2.tgz", "integrity": "sha1-t9ETrueo3Se9IRM8TcJSnfFyHu0=", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.3.1", "resolved": "http://registry.npm.taobao.org/cookie/download/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s=", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-parser": {"version": "1.4.3", "resolved": "http://registry.npm.taobao.org/cookie-parser/download/cookie-parser-1.4.3.tgz", "integrity": "sha1-D+MfoZ0AC5X0qt8fU/3CuKIDuqU=", "dependencies": {"cookie": "0.3.1", "cookie-signature": "1.0.6"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "http://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "node_modules/core-js": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/core-js/-/core-js-1.2.7.tgz", "integrity": "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js."}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cron-parser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/cron-parser/-/cron-parser-2.11.0.tgz", "integrity": "sha1-w79HfgHealaTjWYluS79bOwwqKU=", "dependencies": {"is-nan": "^1.2.1", "moment-timezone": "^0.5.23"}, "engines": {"node": ">=0.8"}}, "node_modules/cross-fetch": {"version": "3.1.5", "resolved": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.5.tgz", "integrity": "sha512-lvb1SBsI0Z7GDwmuid+mU3kWVBwTVUbe7S0H52yaaAdQOXq2YktTCZdlAcNKFzE6QtRz0snpw9bNiPeOIkkQvw==", "dependencies": {"node-fetch": "2.6.7"}}, "node_modules/crypto-random-string": {"version": "2.0.0", "resolved": "https://registry.nlark.com/crypto-random-string/download/crypto-random-string-2.0.0.tgz", "integrity": "sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=", "engines": {"node": ">=8"}}, "node_modules/css": {"version": "1.0.8", "resolved": "http://registry.npm.taobao.org/css/download/css-1.0.8.tgz", "integrity": "sha1-k4aBHKgrzMnuf7WnMrHioxfIo+c=", "dependencies": {"css-parse": "1.0.4", "css-stringify": "1.0.5"}}, "node_modules/css-parse": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/css-parse/download/css-parse-1.0.4.tgz", "integrity": "sha1-OLBQP7+dqfVOnB29pg4UXHcRe90="}, "node_modules/css-stringify": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/css-stringify/download/css-stringify-1.0.5.tgz", "integrity": "sha1-sNBClG2ylTu50pKQCmy19tASIDE="}, "node_modules/debug": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dependencies": {"ms": "0.7.1"}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-response": {"version": "3.3.0", "resolved": "https://registry.npm.taobao.org/decompress-response/download/decompress-response-3.3.0.tgz", "integrity": "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=", "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/deep-extend": {"version": "0.6.0", "resolved": "https://registry.npm.taobao.org/deep-extend/download/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "engines": {"node": ">=4.0.0"}}, "node_modules/defer-to-connect": {"version": "1.1.3", "resolved": "https://registry.nlark.com/defer-to-connect/download/defer-to-connect-1.1.3.tgz", "integrity": "sha1-MxrgUMCNz3ifjIOnuB8O2U9KxZE="}, "node_modules/define-properties": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dependencies": {"object-keys": "^1.0.12"}, "engines": {"node": ">= 0.4"}}, "node_modules/depd": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/depd/download/depd-1.1.0.tgz", "integrity": "sha1-4b2Cxqq2ztlluXuIsX7T5SjKGMM=", "engines": {"node": ">= 0.6"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "node_modules/devtools-protocol": {"version": "0.0.969999", "resolved": "https://registry.npmjs.org/devtools-protocol/-/devtools-protocol-0.0.969999.tgz", "integrity": "sha512-6GfzuDWU0OFAuOvBokXpXPLxjOJ5DZ157Ue3sGQQM3LgAamb8m0R0ruSfN0DDu+XG5XJgT50i6zZ/0o8RglreQ=="}, "node_modules/dicer": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/dicer/-/dicer-0.2.5.tgz", "integrity": "sha1-WZbAhrszIYyBLAkL3cCc0S+stw8=", "dependencies": {"readable-stream": "1.1.x", "streamsearch": "0.1.2"}, "engines": {"node": ">=0.8.0"}}, "node_modules/dicer/node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "node_modules/dicer/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dot-prop": {"version": "5.3.0", "resolved": "https://registry.nlark.com/dot-prop/download/dot-prop-5.3.0.tgz", "integrity": "sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=", "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/duplexer3": {"version": "0.1.4", "resolved": "https://registry.nlark.com/duplexer3/download/duplexer3-0.1.4.tgz", "integrity": "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI="}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "node_modules/ejs": {"version": "2.5.9", "resolved": "https://registry.npm.taobao.org/ejs/download/ejs-2.5.9.tgz?cache=0&sync_timestamp=1612643435705&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fejs%2Fdownload%2Fejs-2.5.9.tgz", "integrity": "sha1-e6JUWCpWDSZ0NxCaaDVBEkdbDOU=", "engines": {"node": ">=0.10.0"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "node_modules/encodeurl": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.1.tgz", "integrity": "sha1-eePVhlU0aQn+bw9Fpd5oEDspTSA=", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.12", "resolved": "https://registry.npmjs.org/encoding/-/encoding-0.1.12.tgz", "integrity": "sha512-bl1LAgiQc4ZWr++pNYUdRe/alecaHFeHxIJ/pNciqGdKXghaTCOwKkbKp6ye7pKZGu/GcaSXFk8PBVhgs+dJdA==", "dependencies": {"iconv-lite": "~0.4.13"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://registry.nlark.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "dependencies": {"once": "^1.4.0"}}, "node_modules/engine.io": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/engine.io/-/engine.io-5.1.1.tgz", "integrity": "sha512-aMWot7H5aC8L4/T8qMYbLdvKlZOdJTH54FxfdFunTGvhMx1BHkJOntWArsVfgAZVwAO9LC2sryPWRcEeUzCe5w==", "dependencies": {"accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.4.1", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~4.0.0", "ws": "~7.4.2"}, "engines": {"node": ">=10.0.0"}}, "node_modules/engine.io-parser": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-4.0.2.tgz", "integrity": "sha512-sHfEQv6nmtJrq6TKuIz5kyEKH/qSdK56H/A+7DnAuUPWosnIZAS2NHNcPLmyjtY3cGS/MqJdZbUjW97JU72iYg==", "dependencies": {"base64-arraybuffer": "0.1.4"}, "engines": {"node": ">=8.0.0"}}, "node_modules/engine.io/node_modules/accepts": {"version": "1.3.7", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz", "integrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/engine.io/node_modules/cookie": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz", "integrity": "sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA==", "engines": {"node": ">= 0.6"}}, "node_modules/engine.io/node_modules/debug": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/engine.io/node_modules/mime-db": {"version": "1.49.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.49.0.tgz", "integrity": "sha512-CIc8j9URtOVApSFCQIF+VBkX1RwXp/oMMOrqdyXSBXq5RWNEsRfyj1kiRnQgmNXmHxPoFIxOroKA3zcU9P+nAA==", "engines": {"node": ">= 0.6"}}, "node_modules/engine.io/node_modules/mime-types": {"version": "2.1.32", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "integrity": "sha512-hJGaVS4G4c9TSMYh2n6SQAGrC4RnfU+daP8G7cSCmaqNjiOoUY0VHCMS42pxnQmVF1GWwFhbHWn3RIxCqTmZ9A==", "dependencies": {"mime-db": "1.49.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/engine.io/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/engine.io/node_modules/negotiator": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==", "engines": {"node": ">= 0.6"}}, "node_modules/envinfo": {"version": "7.8.1", "resolved": "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz", "integrity": "sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==", "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/es6-promise": {"version": "4.2.8", "resolved": "https://registry.npmmirror.com/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w=="}, "node_modules/escape-goat": {"version": "2.1.1", "resolved": "https://registry.npm.taobao.org/escape-goat/download/escape-goat-2.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fescape-goat%2Fdownload%2Fescape-goat-2.1.1.tgz", "integrity": "sha1-Gy3HcANnbEV+x2Cy3GjttkgYhnU=", "engines": {"node": ">=8"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.7.0", "resolved": "http://registry.npm.taobao.org/etag/download/etag-1.7.0.tgz", "integrity": "sha1-A9MLX2fdbmMtKUXTDWZScxo01dg=", "engines": {"node": ">= 0.6"}}, "node_modules/express": {"version": "4.14.0", "resolved": "http://registry.npm.taobao.org/express/download/express-4.14.0.tgz", "integrity": "sha1-we4/Qs3Ikfs9xlCoki1R7IR9DWY=", "dependencies": {"accepts": "~1.3.3", "array-flatten": "1.1.1", "content-disposition": "0.5.1", "content-type": "~1.0.2", "cookie": "0.3.1", "cookie-signature": "1.0.6", "debug": "~2.2.0", "depd": "~1.1.0", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "etag": "~1.7.0", "finalhandler": "0.5.0", "fresh": "0.3.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.1", "path-to-regexp": "0.1.7", "proxy-addr": "~1.1.2", "qs": "6.2.0", "range-parser": "~1.2.0", "send": "0.14.1", "serve-static": "~1.11.1", "type-is": "~1.6.13", "utils-merge": "1.0.0", "vary": "~1.1.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/extract-zip": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz", "integrity": "sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "node_modules/extract-zip/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/extract-zip/node_modules/get-stream": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz", "integrity": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/extract-zip/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/fd-slicer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz", "integrity": "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=", "dependencies": {"pend": "~1.2.0"}}, "node_modules/fetch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fetch/-/fetch-1.1.0.tgz", "integrity": "sha512-5O8TwrGzoNblBG/jtK4NFuZwNCkZX6s5GfRNOaGtm+QGJEuNakSC/i2RW0R93KX6E0jVjNXm6O3CRN4Ql3K+yA==", "dependencies": {"biskviit": "1.0.1", "encoding": "0.1.12"}}, "node_modules/fill-range": {"version": "7.0.1", "resolved": "https://registry.nlark.com/fill-range/download/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "0.5.0", "resolved": "http://registry.npm.taobao.org/finalhandler/download/finalhandler-0.5.0.tgz", "integrity": "sha1-6VCKvs6bbbqHGmlCodeRG5GRGsc=", "dependencies": {"debug": "~2.2.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "statuses": "~1.3.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/follow-redirects": {"version": "1.14.5", "resolved": "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.14.5.tgz", "integrity": "sha1-8JpYSJgdPHcrU5Iwl3hSP42Fw4E=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/forwarded": {"version": "0.1.0", "resolved": "http://registry.npm.taobao.org/forwarded/download/forwarded-0.1.0.tgz", "integrity": "sha1-Ge+YdMSuHCl7zweP3mOgm2aoQ2M=", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/fresh/download/fresh-0.3.0.tgz", "integrity": "sha1-ZR+DjiJCTnVm3hYdg1jKoZn4PU8=", "engines": {"node": ">= 0.6"}}, "node_modules/fs-constants": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npm.taobao.org/fsevents/download/fsevents-2.3.2.tgz", "integrity": "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=", "hasInstallScript": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "node_modules/generic-pool": {"version": "2.5.4", "resolved": "https://registry.npmjs.org/generic-pool/-/generic-pool-2.5.4.tgz", "integrity": "sha1-OMYYhRPhQDCUjsblz2VSPZd5KZs=", "engines": {"node": ">= 0.8.0"}}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "https://registry.nlark.com/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/glob": {"version": "7.1.6", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz", "integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==", "deprecated": "Glob versions prior to v9 are no longer supported", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632953697891&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/global-dirs": {"version": "3.0.0", "resolved": "https://registry.nlark.com/global-dirs/download/global-dirs-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobal-dirs%2Fdownload%2Fglobal-dirs-3.0.0.tgz", "integrity": "sha1-cKdv6E6jFas3sfVXbL3n1I73JoY=", "dependencies": {"ini": "2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/got": {"version": "9.6.0", "resolved": "https://registry.nlark.com/got/download/got-9.6.0.tgz?cache=0&sync_timestamp=1628752491100&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fgot%2Fdownload%2Fgot-9.6.0.tgz", "integrity": "sha1-7fRefWf5lUVwXeH3u+7rEhdl7YU=", "dependencies": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}, "engines": {"node": ">=8.6"}}, "node_modules/graceful-fs": {"version": "4.2.8", "resolved": "https://registry.nlark.com/graceful-fs/download/graceful-fs-4.2.8.tgz?cache=0&sync_timestamp=1628194078324&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.8.tgz", "integrity": "sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo="}, "node_modules/graceful-readlink": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/graceful-readlink/download/graceful-readlink-1.0.1.tgz", "integrity": "sha1-TK+tdrxi8C+gObL5Tpo906ORpyU="}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "https://registry.nlark.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "engines": {"node": ">=4"}}, "node_modules/has-yarn": {"version": "2.1.0", "resolved": "https://registry.nlark.com/has-yarn/download/has-yarn-2.1.0.tgz?cache=0&sync_timestamp=1631298336054&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-yarn%2Fdownload%2Fhas-yarn-2.1.0.tgz", "integrity": "sha1-E34RNUp7W/EapctknPDG8/8rLnc=", "engines": {"node": ">=8"}}, "node_modules/hoopy": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz", "integrity": "sha512-HRcs+2mr52W0K+x8RzcLzuPPmVIKMSv97RGHy0Ea9y/mpcaK+xTrjICA04KAHi4GRzxliNqNJEFYWHghy3rSfQ==", "engines": {"node": ">= 6.0.0"}}, "node_modules/html_codesniffer": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/html_codesniffer/-/html_codesniffer-2.5.1.tgz", "integrity": "sha512-vcz0yAaX/OaV6sdNHuT9alBOKkSxYb8h5Yq26dUqgi7XmCgGUSa7U9PiY1PBXQFMjKv1wVPs5/QzHlGuxPDUGg==", "engines": {"node": ">=6"}}, "node_modules/http-cache-semantics": {"version": "4.1.0", "resolved": "https://registry.nlark.com/http-cache-semantics/download/http-cache-semantics-4.1.0.tgz", "integrity": "sha1-SekcXL82yblLz81xwj1SSex045A="}, "node_modules/http-errors": {"version": "1.5.1", "resolved": "http://registry.npm.taobao.org/http-errors/download/http-errors-1.5.1.tgz", "integrity": "sha1-eIwNLB3iyBuebowBhDtrl+uSB1A=", "dependencies": {"inherits": "2.0.3", "setprototypeof": "1.0.2", "statuses": ">= 1.3.1 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/https-proxy-agent": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz", "integrity": "sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA==", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/https-proxy-agent/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/httpx": {"version": "2.2.7", "resolved": "https://registry.npmmirror.com/httpx/-/httpx-2.2.7.tgz", "integrity": "sha512-Wjh2JOAah0pdczfqL8NC5378G7jMt0Zcpn8U+yyxAiejjlagzSTQgJHuVvka2VNPQlKfoGehYRc79WKq9E4gDw==", "dependencies": {"@types/node": "^14", "debug": "^4.1.1"}}, "node_modules/httpx/node_modules/@types/node": {"version": "14.18.21", "resolved": "https://registry.npmmirror.com/@types/node/-/node-14.18.21.tgz", "integrity": "sha512-x5W9s+8P4XteaxT/jKF0PSb7XEvo5VmqEWgsMlyeY4ZlLK8I6aH6g5TPPyDlLAep+GYf4kefb7HFyc7PAO3m+Q=="}, "node_modules/httpx/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/httpx/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/iconv-lite": {"version": "0.4.13", "resolved": "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.13.tgz", "integrity": "sha1-H4irpKsLFQjoMSrMOTRfNumS4vI=", "engines": {"node": ">=0.8.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/ignore-by-default": {"version": "1.0.1", "resolved": "https://registry.npm.taobao.org/ignore-by-default/download/ignore-by-default-1.0.1.tgz", "integrity": "sha1-SMptcvbGo68Aqa1K5odr44ieKwk="}, "node_modules/import-lazy": {"version": "2.1.0", "resolved": "https://registry.nlark.com/import-lazy/download/import-lazy-2.1.0.tgz", "integrity": "sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://registry.nlark.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.3", "resolved": "http://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "node_modules/ini": {"version": "2.0.0", "resolved": "https://registry.nlark.com/ini/download/ini-2.0.0.tgz", "integrity": "sha1-5f1Vbs3VcmvpePoQAYYurLCpS8U=", "engines": {"node": ">=10"}}, "node_modules/ipaddr.js": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.1.1.tgz", "integrity": "sha1-x5HZX1KynBJH1d+AraObinNkcjA=", "engines": {"node": ">= 0.10"}}, "node_modules/is": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/is/-/is-3.3.0.tgz", "integrity": "sha512-nW24QBoPcFGGHJGUwnfpI7Yc5CdqWNdsyHQszVE/z2pKHXzh7FZ5GWhJqSyaQ9wMkQnsTx+kAI8bHlCX4tKdbg==", "engines": {"node": "*"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://registry.nlark.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-buffer": {"version": "1.1.4", "resolved": "http://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.4.tgz", "integrity": "sha1-z8hszV3FpS+oBIkRHGkgxFfi2Ys=", "deprecated": "This version of 'is-buffer' is out-of-date. You must update to v1.1.6 or newer"}, "node_modules/is-ci": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz?cache=0&sync_timestamp=1635261061017&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-2.0.0.tgz", "integrity": "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=", "dependencies": {"ci-info": "^2.0.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.nlark.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-installed-globally": {"version": "0.4.0", "resolved": "https://registry.npm.taobao.org/is-installed-globally/download/is-installed-globally-0.4.0.tgz", "integrity": "sha1-mg/UB5ScMPhutpWe8beZTtC3tSA=", "dependencies": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-nan": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/is-nan/-/is-nan-1.2.1.tgz", "integrity": "sha1-n69ltvttskt/XAYoR16nH5iEAeI=", "dependencies": {"define-properties": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-npm": {"version": "5.0.0", "resolved": "https://registry.nlark.com/is-npm/download/is-npm-5.0.0.tgz?cache=0&sync_timestamp=1631992770984&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-npm%2Fdownload%2Fis-npm-5.0.0.tgz", "integrity": "sha1-Q+jWXMVuG2f41HJiz2ZwmRk/Rag=", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.nlark.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "engines": {"node": ">=0.12.0"}}, "node_modules/is-obj": {"version": "2.0.0", "resolved": "https://registry.npm.taobao.org/is-obj/download/is-obj-2.0.0.tgz?cache=0&sync_timestamp=1618600919478&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-obj%2Fdownload%2Fis-obj-2.0.0.tgz", "integrity": "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=", "engines": {"node": ">=8"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "https://registry.nlark.com/is-path-inside/download/is-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=", "engines": {"node": ">=8"}}, "node_modules/is-promise": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/is-promise/download/is-promise-2.1.0.tgz", "integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o="}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "node_modules/is-yarn-global": {"version": "0.3.0", "resolved": "https://registry.nlark.com/is-yarn-global/download/is-yarn-global-0.3.0.tgz", "integrity": "sha1-1QLTOCWQ6jAEiTdGdUyJE5lz4jI="}, "node_modules/isarray": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "node_modules/jade": {"version": "1.11.0", "resolved": "http://registry.npm.taobao.org/jade/download/jade-1.11.0.tgz", "integrity": "sha1-nIDlOMEtP7lcjZu5VZ+gzAQEBf0=", "deprecated": "Jade has been renamed to pug, please install the latest version of pug instead of jade", "dependencies": {"character-parser": "1.2.1", "clean-css": "^3.1.9", "commander": "~2.6.0", "constantinople": "~3.0.1", "jstransformer": "0.0.2", "mkdirp": "~0.5.0", "transformers": "2.1.0", "uglify-js": "^2.4.19", "void-elements": "~2.0.1", "with": "~4.0.0"}, "bin": {"jade": "bin/jade.js"}}, "node_modules/jose": {"version": "5.2.4", "resolved": "https://registry.npmmirror.com/jose/-/jose-5.2.4.tgz", "integrity": "sha512-6ScbIk2WWCeXkmzF6bRPmEuaqy1m8SbsRFMa/FLrSCkGIhj8OLVG/IH+XHVmNMx/KUo8cVWEE6oKR4dJ+S0Rkg==", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/js-base64": {"version": "3.7.2", "resolved": "https://registry.npmmirror.com/js-base64/download/js-base64-3.7.2.tgz", "integrity": "sha512-NnRs6dsyqUXejqk/yv2aiXlAvOs56sLkX6nUdeaNezI5LFFLlsZjOThmwnrcwh5ZZRwZlCMnVAY3CvhIhoVEKQ=="}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-bigint": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/json-bigint/-/json-bigint-1.0.0.tgz", "integrity": "sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==", "dependencies": {"bignumber.js": "^9.0.0"}}, "node_modules/json-buffer": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/json-buffer/download/json-buffer-3.0.0.tgz", "integrity": "sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg="}, "node_modules/jsonwebtoken": {"version": "9.0.2", "resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsonwebtoken/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/jsonwebtoken/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jstransformer": {"version": "0.0.2", "resolved": "http://registry.npm.taobao.org/jstransformer/download/jstransformer-0.0.2.tgz", "integrity": "sha1-eq4pqQPRls+glz2IXT5HlH7Ndqs=", "dependencies": {"is-promise": "^2.0.0", "promise": "^6.0.1"}}, "node_modules/jwa": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/jwa/-/jwa-1.4.2.tgz", "integrity": "sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw==", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/keyv": {"version": "3.1.0", "resolved": "https://registry.npm.taobao.org/keyv/download/keyv-3.1.0.tgz", "integrity": "sha1-7MIoSG9pmR5J6UdkhaW+Ho/FxNk=", "dependencies": {"json-buffer": "3.0.0"}}, "node_modules/kind-of": {"version": "3.0.4", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.0.4.tgz", "integrity": "sha1-e47PGKThf4Jp1ztQHJ8jLJaIenQ=", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/kitx": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/kitx/-/kitx-1.3.0.tgz", "integrity": "sha512-fhBqFlXd0GkKTB+8ayLfpzPUw+LHxZlPAukPNBD1Om7JMeInT+/PxCAf1yLagvD+VKoyWhXtJR68xQkX/a0wOQ=="}, "node_modules/kleur": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/kleur/-/kleur-4.1.4.tgz", "integrity": "sha512-8QADVssbrFjivHWQU7KkMgptGTl6WAcSdlbBPY4uNF+mWr6DGcKrvY2w4FQJoXch7+fKMjj0dRrL75vk3k23OA==", "engines": {"node": ">=6"}}, "node_modules/latest-version": {"version": "5.1.0", "resolved": "https://registry.nlark.com/latest-version/download/latest-version-5.1.0.tgz?cache=0&sync_timestamp=1626951703884&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flatest-version%2Fdownload%2Flatest-version-5.1.0.tgz", "integrity": "sha1-EZ3+kI/jjRXfpD7NE/oS7Igy+s4=", "dependencies": {"package-json": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/lazy-cache": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/lazy-cache/download/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4=", "engines": {"node": ">=0.10.0"}}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/lodash.get": {"version": "4.4.2", "resolved": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=", "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "node_modules/lodash.includes": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w=="}, "node_modules/lodash.isboolean": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA=", "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead."}, "node_modules/lodash.isinteger": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA=="}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw=="}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw=="}, "node_modules/lodash.mergewith": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz", "integrity": "sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ=="}, "node_modules/lodash.once": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg=="}, "node_modules/long": {"version": "5.2.3", "resolved": "https://registry.npmmirror.com/long/-/long-5.2.3.tgz", "integrity": "sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q=="}, "node_modules/long-timeout": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/long-timeout/-/long-timeout-0.1.1.tgz", "integrity": "sha1-lyHXiLR+C8taJMLivuGg2lXatRQ="}, "node_modules/longest": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/longest/download/longest-1.0.1.tgz", "integrity": "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=", "engines": {"node": ">=0.10.0"}}, "node_modules/lowercase-keys": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz?cache=0&sync_timestamp=1634551715073&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-1.0.1.tgz", "integrity": "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=", "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "11.1.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-11.1.0.tgz", "integrity": "sha512-QIXZUBJUx+2zHUdQujWejBkcD9+cs94tLn0+YL8UrCh+D5sCXZ4c7LaEH48pNwRY3MLDgqUFyhlCyjJPf1WP0A==", "engines": {"node": "20 || >=22"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://registry.npm.taobao.org/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.0", "resolved": "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1616463603361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "bin": {"semver": "bin/semver.js"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "node_modules/methods": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "engines": {"node": ">= 0.6"}}, "node_modules/mime": {"version": "1.3.4", "resolved": "http://registry.npm.taobao.org/mime/download/mime-1.3.4.tgz", "integrity": "sha1-EV+eO2s9rylZmDyzjxSaLUDrXVM=", "bin": {"mime": "cli.js"}}, "node_modules/mime-db": {"version": "1.25.0", "resolved": "http://registry.npm.taobao.org/mime-db/download/mime-db-1.25.0.tgz", "integrity": "sha1-wY29fHOl2/b0SgJNwNFloeexw5I=", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.13", "resolved": "http://registry.npm.taobao.org/mime-types/download/mime-types-2.1.13.tgz", "integrity": "sha1-4HqqnGxrmnyjASxpADrSWjnpKog=", "dependencies": {"mime-db": "~1.25.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-response": {"version": "1.0.1", "resolved": "https://registry.nlark.com/mimic-response/download/mimic-response-1.0.1.tgz?cache=0&sync_timestamp=1628692524926&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmimic-response%2Fdownload%2Fmimic-response-1.0.1.tgz", "integrity": "sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "0.0.8", "resolved": "http://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}, "node_modules/mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp-classic": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "integrity": "sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A=="}, "node_modules/moment": {"version": "2.29.4", "resolved": "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.25", "resolved": "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.25.tgz", "integrity": "sha1-oRv6L3TgiDJ/LNTAiz5731WVeBA=", "dependencies": {"moment": ">= 2.9.0"}, "engines": {"node": "*"}}, "node_modules/morgan": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.7.0.tgz", "integrity": "sha1-6xDKjlDRq+D409rVwCAdBS2YHGI=", "dependencies": {"basic-auth": "~1.0.3", "debug": "~2.2.0", "depd": "~1.1.0", "on-finished": "~2.3.0", "on-headers": "~1.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ms": {"version": "0.7.1", "resolved": "http://registry.npm.taobao.org/ms/download/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg="}, "node_modules/mssql": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/mssql/-/mssql-3.3.0.tgz", "integrity": "sha1-tuYzf/Ej6Hv4ruHmyDRLU8pdqFY=", "dependencies": {"generic-pool": "^2.2.0", "promise": "^7.0.1", "tedious": "~1.14.0"}, "bin": {"mssql": "bin/mssql"}, "engines": {"node": ">=0.10"}}, "node_modules/mssql/node_modules/asap": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="}, "node_modules/mssql/node_modules/promise": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "dependencies": {"asap": "~2.0.3"}}, "node_modules/multer": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/multer/-/multer-1.4.2.tgz", "integrity": "sha512-xY8pX7V+ybyUpbYMxtjM9KAiD9ixtg5/JkeKUTD6xilfDv0vzzOFcCp4Ljb1UU3tSOM3VTZtKo63OmzOrGi3Cg==", "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10.", "dependencies": {"append-field": "^1.0.0", "busboy": "^0.2.11", "concat-stream": "^1.5.2", "mkdirp": "^0.5.1", "object-assign": "^4.1.1", "on-finished": "^2.3.0", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/mustache": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/mustache/-/mustache-4.2.0.tgz", "integrity": "sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==", "bin": {"mustache": "bin/mustache"}}, "node_modules/mysql": {"version": "2.18.1", "resolved": "https://registry.npmjs.org/mysql/-/mysql-2.18.1.tgz", "integrity": "sha512-B<PERSON>+gk2YWmqp2Uf6k5NFEurwY/0td0cpebAucFpY/3jhrwrVGuxU2uQFCHjU19SJfje0yQvi+rVWdq78hR5lig==", "dependencies": {"bignumber.js": "9.0.0", "readable-stream": "2.3.7", "safe-buffer": "5.1.2", "sqlstring": "2.3.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/mysql/node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "node_modules/mysql/node_modules/readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/mysql/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/negotiator": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/negotiator/download/negotiator-0.6.1.tgz", "integrity": "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk=", "engines": {"node": ">= 0.6"}}, "node_modules/node-fetch": {"version": "2.6.7", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "integrity": "sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-forge": {"version": "1.3.1", "resolved": "https://registry.npmmirror.com/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-jose": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/node-jose/-/node-jose-2.2.0.tgz", "integrity": "sha512-XPCvJRr94SjLrSIm4pbYHKLEaOsDvJCpyFw/6V/KK/IXmyZ6SFBzAUDO9HQf4DB/nTEFcRGH87mNciOP23kFjw==", "dependencies": {"base64url": "^3.0.1", "buffer": "^6.0.3", "es6-promise": "^4.2.8", "lodash": "^4.17.21", "long": "^5.2.0", "node-forge": "^1.2.1", "pako": "^2.0.4", "process": "^0.11.10", "uuid": "^9.0.0"}}, "node_modules/node-jose/node_modules/buffer": {"version": "6.0.3", "resolved": "https://registry.npmmirror.com/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/node-schedule": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/node-schedule/-/node-schedule-1.3.2.tgz", "integrity": "sha1-13Szg+Km9q3lnuzGIlSuoHzXWMs=", "dependencies": {"cron-parser": "^2.7.3", "long-timeout": "0.1.1", "sorted-array-functions": "^1.0.0"}}, "node_modules/node.extend": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/node.extend/-/node.extend-2.0.2.tgz", "integrity": "sha512-pDT4Dchl94/+kkgdwyS2PauDFjZG0Hk0IcHIB+LkW27HLDtdoeMxHTxZh39DYbPP8UflWXWj9JcdDozF+YDOpQ==", "dependencies": {"has": "^1.0.3", "is": "^3.2.1"}, "engines": {"node": ">=0.4.0"}}, "node_modules/nodemailer": {"version": "6.1.1", "resolved": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.1.1.tgz", "integrity": "sha1-CeiO9LNkbwEInF2E0Ae4chQftXU=", "engines": {"node": ">=6.0.0"}}, "node_modules/nodemon": {"version": "2.0.14", "resolved": "https://registry.npmmirror.com/nodemon/download/nodemon-2.0.14.tgz?cache=0&sync_timestamp=1634654540932&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnodemon%2Fdownload%2Fnodemon-2.0.14.tgz", "integrity": "sha1-KHx6L2zYoYsH6UzXduy2qC5LpDk=", "hasInstallScript": true, "dependencies": {"chokidar": "^3.2.2", "debug": "^3.2.6", "ignore-by-default": "^1.0.1", "minimatch": "^3.0.4", "pstree.remy": "^1.1.7", "semver": "^5.7.1", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.3", "update-notifier": "^5.1.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=8.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "node_modules/nodemon/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.nlark.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dependencies": {"ms": "^2.1.1"}}, "node_modules/nodemon/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}, "node_modules/nopt": {"version": "1.0.10", "resolved": "https://registry.npm.taobao.org/nopt/download/nopt-1.0.10.tgz", "integrity": "sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "*"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "4.5.1", "resolved": "https://registry.nlark.com/normalize-url/download/normalize-url-4.5.1.tgz", "integrity": "sha1-DdkM8SiO4dExO4cIHJpZMu5IUYo=", "engines": {"node": ">=8"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "engines": {"node": ">=0.10.0"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "engines": {"node": ">= 0.4"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "http://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/on-headers/download/on-headers-1.0.1.tgz", "integrity": "sha1-ko9dD0cNSTQmUepnlLCFfBAGk/c=", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dependencies": {"wrappy": "1"}}, "node_modules/optimist": {"version": "0.3.7", "resolved": "http://registry.npm.taobao.org/optimist/download/optimist-0.3.7.tgz", "integrity": "sha1-yQlBrVnkJzMokjB00s8ufLxuwNk=", "dependencies": {"wordwrap": "~0.0.2"}}, "node_modules/p-cancelable": {"version": "1.1.0", "resolved": "https://registry.nlark.com/p-cancelable/download/p-cancelable-1.1.0.tgz?cache=0&sync_timestamp=1622467988667&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fp-cancelable%2Fdownload%2Fp-cancelable-1.1.0.tgz", "integrity": "sha1-0HjRWjr0CSIMiG8dmgyi5EGrJsw=", "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-timeout": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-timeout/-/p-timeout-4.1.0.tgz", "integrity": "sha512-+/wmHtzJuWii1sXn3HCuH/FTwGhrp4tmJTxSKJbfS+vkipci6osxXM5mY0jUiRzWKMTgUT8l7HFbeSwZAynqHw==", "engines": {"node": ">=10"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "engines": {"node": ">=6"}}, "node_modules/pa11y": {"version": "6.2.3", "resolved": "https://registry.npmjs.org/pa11y/-/pa11y-6.2.3.tgz", "integrity": "sha512-69JoUlfW2QVmrgQAm+17XBxIvmd1u0ImFBYIHPyjC61CzAkmxO3kkbqDVxIcl0OKLvAMYSMbvfCH8kMFE9xsbg==", "dependencies": {"axe-core": "~4.2.1", "bfj": "~7.0.2", "commander": "~8.0.0", "envinfo": "~7.8.1", "html_codesniffer": "~2.5.1", "kleur": "~4.1.4", "mustache": "~4.2.0", "node.extend": "~2.0.2", "p-timeout": "~4.1.0", "puppeteer": "~9.1.1", "semver": "~7.3.5"}, "bin": {"pa11y": "bin/pa11y.js"}, "engines": {"node": ">=12"}}, "node_modules/pa11y/node_modules/commander": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-8.0.0.tgz", "integrity": "sha512-Xvf85aAtu6v22+E5hfVoLHqyul/jyxh91zvqk/ioJTQuJR7Z78n7H558vMPKanPSRgIEeZemT92I2g9Y8LPbSQ==", "engines": {"node": ">= 12"}}, "node_modules/pa11y/node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/pa11y/node_modules/devtools-protocol": {"version": "0.0.869402", "resolved": "https://registry.npmjs.org/devtools-protocol/-/devtools-protocol-0.0.869402.tgz", "integrity": "sha512-VvlVYY+VDJe639yHs5PHISzdWTLL3Aw8rO4cvUtwvoxFd6FHbE4OpHHcde52M6096uYYazAmd4l0o5VuFRO2WA=="}, "node_modules/pa11y/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/pa11y/node_modules/puppeteer": {"version": "9.1.1", "resolved": "https://registry.npmjs.org/puppeteer/-/puppeteer-9.1.1.tgz", "integrity": "sha512-W+nOulP2tYd/ZG99WuZC/I5ljjQQ7EUw/jQGcIb9eu8mDlZxNY2SgcJXTLG9h5gRvqA3uJOe4hZXYsd3EqioMw==", "deprecated": "< 22.8.2 is no longer supported", "hasInstallScript": true, "dependencies": {"debug": "^4.1.0", "devtools-protocol": "0.0.869402", "extract-zip": "^2.0.0", "https-proxy-agent": "^5.0.0", "node-fetch": "^2.6.1", "pkg-dir": "^4.2.0", "progress": "^2.0.1", "proxy-from-env": "^1.1.0", "rimraf": "^3.0.2", "tar-fs": "^2.0.0", "unbzip2-stream": "^1.3.3", "ws": "^7.2.3"}, "engines": {"node": ">=10.18.1"}}, "node_modules/pa11y/node_modules/semver": {"version": "7.3.7", "resolved": "https://registry.npmjs.org/semver/-/semver-7.3.7.tgz", "integrity": "sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/pa11y/node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/package-json": {"version": "6.5.0", "resolved": "https://registry.nlark.com/package-json/download/package-json-6.5.0.tgz?cache=0&sync_timestamp=1624549851966&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpackage-json%2Fdownload%2Fpackage-json-6.5.0.tgz", "integrity": "sha1-b+7ayjXnVyWHbQsOZJdGl/7RRbA=", "dependencies": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}, "engines": {"node": ">=8"}}, "node_modules/package-json/node_modules/semver": {"version": "6.3.0", "resolved": "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1616463603361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "bin": {"semver": "bin/semver.js"}}, "node_modules/pako": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug=="}, "node_modules/parseurl": {"version": "1.3.1", "resolved": "http://registry.npm.taobao.org/parseurl/download/parseurl-1.3.1.tgz", "integrity": "sha1-yKuMkiO6NIiKpkopeyiFO+wY2lY=", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "engines": {"node": ">=0.10.0"}}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "http://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "node_modules/pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA="}, "node_modules/picomatch": {"version": "2.3.0", "resolved": "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz?cache=0&sync_timestamp=1621648246651&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpicomatch%2Fdownload%2Fpicomatch-2.3.0.tgz", "integrity": "sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pnpm": {"version": "10.11.0", "resolved": "https://registry.npmjs.org/pnpm/-/pnpm-10.11.0.tgz", "integrity": "sha512-ZUBYP0HMX2KOs9l3Ps7oAvT575kjzEW2mJD7R5kdSwkpZGlOw6T3OKQgyRijMwYsi5JdMS9C5PDCY+tgNVH5dw==", "bin": {"pnpm": "bin/pnpm.cjs", "pnpx": "bin/pnpx.cjs"}, "engines": {"node": ">=18.12"}, "funding": {"url": "https://opencollective.com/pnpm"}}, "node_modules/prepend-http": {"version": "2.0.0", "resolved": "https://registry.nlark.com/prepend-http/download/prepend-http-2.0.0.tgz?cache=0&sync_timestamp=1628547439455&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprepend-http%2Fdownload%2Fprepend-http-2.0.0.tgz", "integrity": "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=", "engines": {"node": ">=4"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://registry.npmmirror.com/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "1.0.7", "resolved": "http://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-1.0.7.tgz", "integrity": "sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M="}, "node_modules/progress": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "6.1.0", "resolved": "http://registry.npm.taobao.org/promise/download/promise-6.1.0.tgz", "integrity": "sha1-LOcp9rlLRcJoka0GAsXJDgTG7vY=", "dependencies": {"asap": "~1.0.0"}}, "node_modules/proxy-addr": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/proxy-addr/download/proxy-addr-1.1.2.tgz", "integrity": "sha1-tMxfImENlTWCTBI675089zxAujc=", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.1.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/psl": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz", "integrity": "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="}, "node_modules/pstree.remy": {"version": "1.1.8", "resolved": "https://registry.nlark.com/pstree.remy/download/pstree.remy-1.1.8.tgz", "integrity": "sha1-wkIiT0pnwh9oaDm720rCgrg3PTo="}, "node_modules/pump": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz", "integrity": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pupa": {"version": "2.1.1", "resolved": "https://registry.nlark.com/pupa/download/pupa-2.1.1.tgz?cache=0&sync_timestamp=1631543593292&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpupa%2Fdownload%2Fpupa-2.1.1.tgz", "integrity": "sha1-9ej9SvwsXZeCj6pSNUnth0SiDWI=", "dependencies": {"escape-goat": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/puppeteer": {"version": "13.5.2", "resolved": "https://registry.npmjs.org/puppeteer/-/puppeteer-13.5.2.tgz", "integrity": "sha512-DJAyXODBikZ3xPs8C35CtExEw78LZR9RyelGDAs0tX1dERv3OfW7qpQ9VPBgsfz+hG2HiMTO/Tyf7BuMVWsrxg==", "deprecated": "< 22.8.2 is no longer supported", "hasInstallScript": true, "dependencies": {"cross-fetch": "3.1.5", "debug": "4.3.4", "devtools-protocol": "0.0.969999", "extract-zip": "2.0.1", "https-proxy-agent": "5.0.0", "pkg-dir": "4.2.0", "progress": "2.0.3", "proxy-from-env": "1.1.0", "rimraf": "3.0.2", "tar-fs": "2.1.1", "unbzip2-stream": "1.4.3", "ws": "8.5.0"}, "engines": {"node": ">=10.18.1"}}, "node_modules/puppeteer/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/puppeteer/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/puppeteer/node_modules/ws": {"version": "8.5.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.5.0.tgz", "integrity": "sha512-BWX0SWVgLPzYwF8lTzEy1egjhS4S4OEAHfsO8o65WOVsrnSRGaSiUaa9e0ggGlkMTtBlmOpEXiie9RUcBO86qg==", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/qs": {"version": "6.2.0", "resolved": "http://registry.npm.taobao.org/qs/download/qs-6.2.0.tgz", "integrity": "sha1-O3hIwDwt7OaalSKw+ujEEm10Xzs=", "engines": {"node": ">=0.6"}}, "node_modules/range-parser": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/range-parser/download/range-parser-1.2.0.tgz", "integrity": "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4=", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.1.7", "resolved": "http://registry.npm.taobao.org/raw-body/download/raw-body-2.1.7.tgz", "integrity": "sha1-rf6s4uT7MJgFgBTQjActzFl1h3Q=", "dependencies": {"bytes": "2.4.0", "iconv-lite": "0.4.13", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rc": {"version": "1.2.8", "resolved": "https://registry.npm.taobao.org/rc/download/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/rc/node_modules/ini": {"version": "1.3.8", "resolved": "https://registry.nlark.com/ini/download/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw="}, "node_modules/rc/node_modules/minimist": {"version": "1.2.5", "resolved": "https://registry.nlark.com/minimist/download/minimist-1.2.5.tgz?cache=0&sync_timestamp=1618847181284&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimist%2Fdownload%2Fminimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "node_modules/readable-stream": {"version": "2.2.2", "resolved": "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.2.2.tgz", "integrity": "sha1-qeb+w8fdqF+LsbO6cChgRVb8gl4=", "dependencies": {"buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://registry.nlark.com/readdirp/download/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="}, "node_modules/registry-auth-token": {"version": "4.2.1", "resolved": "https://registry.npm.taobao.org/registry-auth-token/download/registry-auth-token-4.2.1.tgz", "integrity": "sha1-bXtABkQZGJcszV/tzUHcMix5slA=", "dependencies": {"rc": "^1.2.8"}, "engines": {"node": ">=6.0.0"}}, "node_modules/registry-url": {"version": "5.1.0", "resolved": "https://registry.npm.taobao.org/registry-url/download/registry-url-5.1.0.tgz?cache=0&sync_timestamp=1618682318998&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregistry-url%2Fdownload%2Fregistry-url-5.1.0.tgz", "integrity": "sha1-6YM0tQ1UNLgRNrROxjjZwgCcUAk=", "dependencies": {"rc": "^1.2.8"}, "engines": {"node": ">=8"}}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "http://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "engines": {"node": ">=0.10"}}, "node_modules/responselike": {"version": "1.0.2", "resolved": "https://registry.nlark.com/responselike/download/responselike-1.0.2.tgz", "integrity": "sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec=", "dependencies": {"lowercase-keys": "^1.0.0"}}, "node_modules/right-align": {"version": "0.1.3", "resolved": "http://registry.npm.taobao.org/right-align/download/right-align-0.1.3.tgz", "integrity": "sha1-YTObci/mo1FWiSENJOFMlhSGE+8=", "dependencies": {"align-text": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "node_modules/sax": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="}, "node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==", "bin": {"semver": "bin/semver"}}, "node_modules/semver-diff": {"version": "3.1.1", "resolved": "https://registry.nlark.com/semver-diff/download/semver-diff-3.1.1.tgz", "integrity": "sha1-Bfd85Z8yXgDicGr9Z7tQbdscoys=", "dependencies": {"semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/semver-diff/node_modules/semver": {"version": "6.3.0", "resolved": "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1616463603361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "bin": {"semver": "bin/semver.js"}}, "node_modules/send": {"version": "0.14.1", "resolved": "http://registry.npm.taobao.org/send/download/send-0.14.1.tgz", "integrity": "sha1-qVSYQyU5L1FTKndgdg5FlZjIn3o=", "dependencies": {"debug": "~2.2.0", "depd": "~1.1.0", "destroy": "~1.0.4", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "etag": "~1.7.0", "fresh": "0.3.0", "http-errors": "~1.5.0", "mime": "1.3.4", "ms": "0.7.1", "on-finished": "~2.3.0", "range-parser": "~1.2.0", "statuses": "~1.3.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-favicon": {"version": "2.3.2", "resolved": "http://registry.npm.taobao.org/serve-favicon/download/serve-favicon-2.3.2.tgz", "integrity": "sha1-3UGeJo3gEqtysxnTN/IQUBP5OB8=", "dependencies": {"etag": "~1.7.0", "fresh": "0.3.0", "ms": "0.7.2", "parseurl": "~1.3.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-favicon/node_modules/ms": {"version": "0.7.2", "resolved": "http://registry.npm.taobao.org/ms/download/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U="}, "node_modules/serve-static": {"version": "1.11.1", "resolved": "http://registry.npm.taobao.org/serve-static/download/serve-static-1.11.1.tgz", "integrity": "sha1-1sznaTUF9zPHWd5Xvvwa92wPCAU=", "dependencies": {"encodeurl": "~1.0.1", "escape-html": "~1.0.3", "parseurl": "~1.3.1", "send": "0.14.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.0.2.tgz", "integrity": "sha1-gaVSFB7BBLiOic44MQOtXGZWTQg="}, "node_modules/signal-exit": {"version": "3.0.5", "resolved": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632948384106&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "integrity": "sha1-nj6MwMdamUcrRDIQM6dwLnc4JS8="}, "node_modules/socket.io": {"version": "4.1.3", "resolved": "https://registry.npmjs.org/socket.io/-/socket.io-4.1.3.tgz", "integrity": "sha512-tLkaY13RcO4nIRh1K2hT5iuotfTaIQw7cVIe0FUykN3SuQi0cm7ALxuyT5/CtDswOMWUzMGTibxYNx/gU7In+Q==", "dependencies": {"@types/cookie": "^0.4.0", "@types/cors": "^2.8.10", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "~2.0.0", "debug": "~4.3.1", "engine.io": "~5.1.1", "socket.io-adapter": "~2.3.1", "socket.io-parser": "~4.0.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/socket.io-adapter": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-2.3.1.tgz", "integrity": "sha512-8cVkRxI8Nt2wadkY6u60Y4rpW3ejA1rxgcK2JuyIhmF+RMNpTy1QRtkHIDUOf3B4HlQwakMsWbKftMv/71VMmw=="}, "node_modules/socket.io-parser": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.0.4.tgz", "integrity": "sha512-t+b0SS+IxG7Rxzda2EVvyBZbvFPBCjJoyHuE0P//7OAsN23GItzDRdWa6ALxZI/8R5ygK7jAR6t028/z+7295g==", "dependencies": {"@types/component-emitter": "^1.2.10", "component-emitter": "~1.3.0", "debug": "~4.3.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/socket.io-parser/node_modules/debug": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/socket.io-parser/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/socket.io/node_modules/accepts": {"version": "1.3.7", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz", "integrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/socket.io/node_modules/debug": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/socket.io/node_modules/mime-db": {"version": "1.49.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.49.0.tgz", "integrity": "sha512-CIc8j9URtOVApSFCQIF+VBkX1RwXp/oMMOrqdyXSBXq5RWNEsRfyj1kiRnQgmNXmHxPoFIxOroKA3zcU9P+nAA==", "engines": {"node": ">= 0.6"}}, "node_modules/socket.io/node_modules/mime-types": {"version": "2.1.32", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "integrity": "sha512-hJGaVS4G4c9TSMYh2n6SQAGrC4RnfU+daP8G7cSCmaqNjiOoUY0VHCMS42pxnQmVF1GWwFhbHWn3RIxCqTmZ9A==", "dependencies": {"mime-db": "1.49.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/socket.io/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/socket.io/node_modules/negotiator": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.2.tgz", "integrity": "sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==", "engines": {"node": ">= 0.6"}}, "node_modules/sorted-array-functions": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/sorted-array-functions/-/sorted-array-functions-1.2.0.tgz", "integrity": "sha1-QyZbIdbphbffMWIbHBHMaNjvx8M="}, "node_modules/source-map": {"version": "0.4.4", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/sprintf": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/sprintf/-/sprintf-0.1.5.tgz", "integrity": "sha1-j4PjmpMXwaUCy324BQ5Rxnn27c8=", "deprecated": "The sprintf package is deprecated in favor of sprintf-js.", "engines": {"node": ">=0.2.4"}}, "node_modules/sqlstring": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/sqlstring/-/sqlstring-2.3.1.tgz", "integrity": "sha1-R1OT/56RR5rqYtyvDKPRSYOn+0A=", "engines": {"node": ">= 0.6"}}, "node_modules/statuses": {"version": "1.3.1", "resolved": "http://registry.npm.taobao.org/statuses/download/statuses-1.3.1.tgz", "integrity": "sha1-+vUbnrdKrvOzrPStX2Gr8ky3uT4=", "engines": {"node": ">= 0.6"}}, "node_modules/streamsearch": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo=", "engines": {"node": ">=0.8.0"}}, "node_modules/string_decoder": {"version": "0.10.31", "resolved": "http://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.nlark.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-json-comments%2Fdownload%2Fstrip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "engines": {"node": ">=0.10.0"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "https://registry.nlark.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1626703342506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/swagger-jsdoc": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/swagger-jsdoc/-/swagger-jsdoc-6.1.0.tgz", "integrity": "sha512-xgep5M8Gq31MxpCbQLvJZpNqHfGPfI+sILCzujZbEXIQp2COtkZgoGASs0gacRs4xHmLDH+GuMGdorPITSG4tA==", "dependencies": {"commander": "6.2.0", "doctrine": "3.0.0", "glob": "7.1.6", "lodash.mergewith": "^4.6.2", "swagger-parser": "10.0.2", "yaml": "2.0.0-1"}, "bin": {"swagger-jsdoc": "bin/swagger-jsdoc.js"}, "engines": {"node": ">=12.0.0"}}, "node_modules/swagger-jsdoc/node_modules/commander": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/commander/-/commander-6.2.0.tgz", "integrity": "sha512-zP4jEKbe8SHzKJYQmq8Y9gYjtO/POJLgIdKgV7B9qNmABVFVc+ctqSX6iXh4mCpJfRBOabiZ2YKPg8ciDw6C+Q==", "engines": {"node": ">= 6"}}, "node_modules/swagger-parser": {"version": "10.0.2", "resolved": "https://registry.npmjs.org/swagger-parser/-/swagger-parser-10.0.2.tgz", "integrity": "sha512-9jHkHM+QXyLGFLk1DkXBwV+4HyNm0Za3b8/zk/+mjr8jgOSiqm3FOTHBSDsBjtn9scdL+8eWcHdupp2NLM8tDw==", "dependencies": {"@apidevtools/swagger-parser": "10.0.2"}, "engines": {"node": ">=10"}}, "node_modules/swagger-ui-dist": {"version": "3.52.0", "resolved": "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-3.52.0.tgz", "integrity": "sha512-SGfhW8FCih00QG59PphdeAUtTNw7HS5k3iPqDZowerPw9mcbhKchUb12kbROk99c1X6RTWW1gB1kqgfnYGuCSg=="}, "node_modules/swagger-ui-express": {"version": "4.1.6", "resolved": "https://registry.npmjs.org/swagger-ui-express/-/swagger-ui-express-4.1.6.tgz", "integrity": "sha512-Xs2BGGudvDBtL7RXcYtNvHsFtP1DBFPMJFRxHe5ez/VG/rzVOEjazJOOSc/kSCyxreCTKfJrII6MJlL9a6t8vw==", "dependencies": {"swagger-ui-dist": "^3.18.1"}, "engines": {"node": ">= v0.10.32"}, "peerDependencies": {"express": ">=4.0.0"}}, "node_modules/tar-fs": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.1.tgz", "integrity": "sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==", "dependencies": {"chownr": "^1.1.1", "mkdirp-classic": "^0.5.2", "pump": "^3.0.0", "tar-stream": "^2.1.4"}}, "node_modules/tar-stream": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz", "integrity": "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==", "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/tar-stream/node_modules/bl": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "integrity": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/tar-stream/node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/tar-stream/node_modules/readable-stream": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/tar-stream/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/tar-stream/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/tedious": {"version": "1.14.0", "resolved": "https://registry.npmjs.org/tedious/-/tedious-1.14.0.tgz", "integrity": "sha1-wFwwO4Zoz5HlWmSTt0SGavYZh94=", "dependencies": {"babel-runtime": "^5.8.19", "big-number": "0.3.1", "bl": "^1.0.0", "iconv-lite": "^0.4.11", "readable-stream": "^2.0.2", "semver": "^5.1.0", "sprintf": "0.1.5"}, "engines": {"node": ">= 0.10"}}, "node_modules/through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "node_modules/to-readable-stream": {"version": "1.0.0", "resolved": "https://registry.nlark.com/to-readable-stream/download/to-readable-stream-1.0.0.tgz?cache=0&sync_timestamp=1619072442497&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fto-readable-stream%2Fdownload%2Fto-readable-stream-1.0.0.tgz", "integrity": "sha1-zgqgwvPfat+FLvtASng+d8BHV3E=", "engines": {"node": ">=6"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.nlark.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/touch": {"version": "3.1.0", "resolved": "https://registry.nlark.com/touch/download/touch-3.1.0.tgz", "integrity": "sha1-/jZfX3XsntTlaCXgu3bSSrdK+Ds=", "dependencies": {"nopt": "~1.0.10"}, "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="}, "node_modules/transformers": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/transformers/download/transformers-2.1.0.tgz", "integrity": "sha1-XSPLNVYd2F3Gf7hIIwm0fVPM6ac=", "deprecated": "Deprecated, use jstransformer", "dependencies": {"css": "~1.0.8", "promise": "~2.0", "uglify-js": "~2.2.5"}}, "node_modules/transformers/node_modules/is-promise": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-promise/download/is-promise-1.0.1.tgz", "integrity": "sha1-MVc3YcBX4zwukaq56W2gjO++duU="}, "node_modules/transformers/node_modules/promise": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/promise/download/promise-2.0.0.tgz", "integrity": "sha1-RmSKqdYFr10ucMMCS/WUNtoCuA4=", "dependencies": {"is-promise": "~1"}}, "node_modules/transformers/node_modules/source-map": {"version": "0.1.43", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/transformers/node_modules/uglify-js": {"version": "2.2.5", "resolved": "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.2.5.tgz", "integrity": "sha1-puAqcNg5eSuXgEiLe4sYTAlcmcc=", "dependencies": {"optimist": "~0.3.5", "source-map": "~0.1.7"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.4.0"}}, "node_modules/tryer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz", "integrity": "sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA=="}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.14", "resolved": "http://registry.npm.taobao.org/type-is/download/type-is-1.6.14.tgz", "integrity": "sha1-4hljnBfe0coHiQkt1UoDgmuBfLI=", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.13"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "resolved": "https://registry.nlark.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/uglify-js": {"version": "2.7.5", "resolved": "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.7.5.tgz", "integrity": "sha1-RhLAx7qu4rp8SH3kkErhIgefLKg=", "dependencies": {"async": "~0.2.6", "source-map": "~0.5.1", "uglify-to-browserify": "~1.0.0", "yargs": "~3.10.0"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/uglify-js/node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha1-trvgsGdLnXGXCMo43owjfLUmw9E="}, "node_modules/uglify-js/node_modules/source-map": {"version": "0.5.6", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.5.6.tgz", "integrity": "sha1-dc449SvwczxafwwRjYEzSiu19BI=", "engines": {"node": ">=0.10.0"}}, "node_modules/uglify-to-browserify": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz", "integrity": "sha1-bgkk1r2mta/jSeOabWMoUKD4grc="}, "node_modules/unbzip2-stream": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz", "integrity": "sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg==", "dependencies": {"buffer": "^5.2.1", "through": "^2.3.8"}}, "node_modules/undefsafe": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/undefsafe/download/undefsafe-2.0.5.tgz", "integrity": "sha1-OHM7kye9zSJtuIn7cjpu/RYubiw="}, "node_modules/unique-string": {"version": "2.0.0", "resolved": "https://registry.npm.taobao.org/unique-string/download/unique-string-2.0.0.tgz", "integrity": "sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "engines": {"node": ">= 0.8"}}, "node_modules/update-notifier": {"version": "5.1.0", "resolved": "https://registry.nlark.com/update-notifier/download/update-notifier-5.1.0.tgz", "integrity": "sha1-SrDXx/NqIx3XMWz3cpMT8CFNmtk=", "dependencies": {"boxen": "^5.0.0", "chalk": "^4.1.0", "configstore": "^5.0.1", "has-yarn": "^2.1.0", "import-lazy": "^2.1.0", "is-ci": "^2.0.0", "is-installed-globally": "^0.4.0", "is-npm": "^5.0.0", "is-yarn-global": "^0.3.0", "latest-version": "^5.1.0", "pupa": "^2.1.1", "semver": "^7.3.4", "semver-diff": "^3.1.1", "xdg-basedir": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/yeoman/update-notifier?sponsor=1"}}, "node_modules/update-notifier/node_modules/semver": {"version": "7.3.5", "resolved": "https://registry.npm.taobao.org/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1616463603361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz", "integrity": "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/update-notifier/node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/url-parse-lax": {"version": "3.0.0", "resolved": "https://registry.nlark.com/url-parse-lax/download/url-parse-lax-3.0.0.tgz?cache=0&sync_timestamp=1628547550655&other_urls=https%3A%2F%2Fregistry.nlark.com%2Furl-parse-lax%2Fdownload%2Furl-parse-lax-3.0.0.tgz", "integrity": "sha1-FrXK/Afb42dsGxmZF3gj1lA6yww=", "dependencies": {"prepend-http": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "node_modules/utils-merge": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.0.tgz", "integrity": "sha1-ApT7kiu5N1FTVBxPcJYjHyh8ivg=", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "9.0.1", "resolved": "https://registry.npmmirror.com/uuid/-/uuid-9.0.1.tgz", "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/validator": {"version": "13.6.0", "resolved": "https://registry.npmjs.org/validator/-/validator-13.6.0.tgz", "integrity": "sha512-gVgKbdbHgtxpRyR8K0O6oFZPhhB5tT1jeEHZR0Znr9Svg03U0+r9DXWMrnRAB+HtCStDQKlaIZm42tVsVjqtjg==", "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/vary/download/vary-1.1.0.tgz", "integrity": "sha1-4eWv+70WrnaN0mdDlLmtMCJlMUA=", "engines": {"node": ">= 0.8"}}, "node_modules/void-elements": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/void-elements/download/void-elements-2.0.1.tgz", "integrity": "sha1-wGavtYK7HLQSjWDqkjkulNXp2+w=", "engines": {"node": ">=0.10.0"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/widest-line": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/widest-line/download/widest-line-3.1.0.tgz?cache=0&sync_timestamp=1634023966185&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwidest-line%2Fdownload%2Fwidest-line-3.1.0.tgz", "integrity": "sha1-gpIzO79my0X/DeFgOxNreuFJbso=", "dependencies": {"string-width": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/window-size": {"version": "0.1.0", "resolved": "http://registry.npm.taobao.org/window-size/download/window-size-0.1.0.tgz", "integrity": "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=", "engines": {"node": ">= 0.8.0"}}, "node_modules/with": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/with/download/with-4.0.3.tgz", "integrity": "sha1-7v0VTp550sjTQXtkeo8U2f7M4U4=", "dependencies": {"acorn": "^1.0.1", "acorn-globals": "^1.0.3"}}, "node_modules/with/node_modules/acorn": {"version": "1.2.2", "resolved": "http://registry.npm.taobao.org/acorn/download/acorn-1.2.2.tgz", "integrity": "sha1-yM4n3grMdtiW0rH6099YjZ6C8BQ=", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/wordwrap": {"version": "0.0.3", "resolved": "http://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.3.tgz", "integrity": "sha1-o9XabNXAvAAI03I0u68b7WMFkQc=", "engines": {"node": ">=0.4.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.nlark.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&sync_timestamp=1631557201275&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "node_modules/write-file-atomic": {"version": "3.0.3", "resolved": "https://registry.nlark.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz?cache=0&sync_timestamp=1618847057132&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrite-file-atomic%2Fdownload%2Fwrite-file-atomic-3.0.3.tgz", "integrity": "sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/ws": {"version": "7.4.6", "resolved": "https://registry.npmjs.org/ws/-/ws-7.4.6.tgz", "integrity": "sha512-YmhHDO4MzaDLB+M9ym/mDA5z0naX8j7SIlT8f8z+I0VtzsRbekxEutHSme7NPS2qE8StCYQNUnfWdXta/Yu85A==", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xdg-basedir": {"version": "4.0.0", "resolved": "https://registry.nlark.com/xdg-basedir/download/xdg-basedir-4.0.0.tgz", "integrity": "sha1-S8jZmEQDaWIl74OhVzy7y0552xM=", "engines": {"node": ">=8"}}, "node_modules/xml2js": {"version": "0.4.23", "resolved": "https://registry.npmmirror.com/xml2js/-/xml2js-0.4.23.tgz", "integrity": "sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "resolved": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==", "engines": {"node": ">=4.0"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==", "engines": {"node": ">=0.4"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}, "node_modules/yaml": {"version": "2.0.0-1", "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-1.tgz", "integrity": "sha512-W7h5dEhywMKenDJh2iX/LABkbFnBxasD27oyXWDS/feDsxiw0dD5ncXdYXgkvAsXIY2MpW/ZKkr9IU30DBdMNQ==", "engines": {"node": ">= 6"}}, "node_modules/yargs": {"version": "3.10.0", "resolved": "http://registry.npm.taobao.org/yargs/download/yargs-3.10.0.tgz", "integrity": "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=", "dependencies": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}}, "node_modules/yauzl": {"version": "2.10.0", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz", "integrity": "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/z-schema": {"version": "4.2.4", "resolved": "https://registry.npmjs.org/z-schema/-/z-schema-4.2.4.tgz", "integrity": "sha512-YvBeW5RGNeNzKOUJs3rTL4+9rpcvHXt5I051FJbOcitV8bl40pEfcG0Q+dWSwS0/BIYrMZ/9HHoqLllMkFhD0w==", "dependencies": {"lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "validator": "^13.6.0"}, "bin": {"z-schema": "bin/z-schema"}, "engines": {"node": ">=6.0.0"}, "optionalDependencies": {"commander": "^2.7.1"}}, "node_modules/z-schema/node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "optional": true}}}