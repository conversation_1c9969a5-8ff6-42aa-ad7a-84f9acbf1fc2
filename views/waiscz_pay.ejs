<!DOCTYPE html>
<html>
<head>
    <title></title>
    <link rel='stylesheet' href='/stylesheets/style_pay.css' />
    <script src='/javascripts/jquery.min.js'></script>

</head>
<body>
<script type="text/javascript">
    
</script>
    <h1><%= name %></h1>

    <% for(var i = 0; i < data.length; i++) { 
        function format(num, cent) {
            if (num != null) {
                return (num.toFixed(cent) + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
            } else {
                return num;
            }
        }
    %>
        <div class="scheme">
        <div class="scheme-title">
            <span class="title">Salary Slip</span> -
            <span class="title"><%= data[i].income_year + '-' + data[i].income_month %></span>
        </div>
        <div class="scheme-content">
            <div class="cat" style="border-top: 0;">
                <div class="cat-title">Other_Item</div>
                <div class="cat-content">
                    <span class="project" title="员工净收入">Staff Net Income员工净收入</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Staff_Net_Income, 2) %></span>

                    <span class="project" title="Expat">Expat</span><span class="pay" style="color:blue;text-align:right;"><%= data[i].local %></span>

                    <span class="project" title="应税收入抵免">Taxable_Income_Tax_Credit应税收入抵免</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Taxable_Income_Tax_Credit, 2) %></span>

                    <span class="project" title="Tax_Base个税基数">Tax_Base个税基数</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Tax_Base, 2) %></span>

                    <span class="project" title="应纳税所得额">Taxable_Income应纳税所得额</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Taxable_Income, 2) %></span>

                    <span class="project" title="Service_IIT劳务个税">Service_IIT劳务个税</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Service_IIT, 2) %></span>

                    <span class="project" title="离职补偿金个税">Severance_Tax离职补偿金个税</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Severance_Tax, 2) %></span>

                    <span class="project" title="Staff_Cost员工总成本">Staff_Cost员工总成本</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Staff_Cost, 2) %></span>

                </div>
            </div>
            <div class="cat" style="border-top: 0;">
            <div class="cat-title">Payable_Item</div>
            <div class="cat-content">
                <span class="project" title="Contract_Salary合同工资">Contract_Salary合同工资</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Contract_Salary, 2) %></span>

                <span class="project" title="Service_Salary劳务工资">Service_Salary劳务工资</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Service_Salary, 2) %></span>

                <span class="project" title="Taxable_Income应税收入">Taxable_Income应税收入</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Taxable_Income, 2) %></span>

                <span class="project" title="Position_Subsidy岗位津贴">Position_Subsidy岗位津贴</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Position_Subsidy, 2) %></span>

                <span class="project" title="Installment_Bonus">Installment_Bonus</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].installment_bonus, 2) %></span>

                <span class="project" title="year_end_bonus">Year_end_bonus</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Year_End_Bonus, 2) %></span>


            </div>
        </div>
            <div class="cat" style="border-top: 0;">
                <div class="cat-title">Attendance Item</div>
                <div class="cat-content">
                    <span class="project" title="司龄">Service Year司龄</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Service_Year, 2) %></span>
                </div>
            </div>
            <div class="cat">
                <div class="cat-title">After_Tax_Item</div>
                <div class="cat-content">
                    <span class="project" title="Salary_after_IIT税后工资">Salary_after_IIT税后工资</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Salary_After_Tax, 2) %></span>
                </div>
				<div class="cat-content">
                    <span class="project" title="After tax adjustment ">After tax adjustment </span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].After_Tax_Adjustment, 2) %></span>
                </div>
            </div>
            <div class="cat">
                <div class="cat-title">Deduction_Item</div>
                <div class="cat-content">
                    <span class="project" title="Pension_Individual养老金个人">Pension_Individual养老金个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Pension_Individual, 2) %></span>

                    <span class="project" title="Medical_Insurance_Individual医保个人">Medical_Insurance_Individual医保个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Medical_Individual, 2) %></span>

                    <span class="project" title="Unemployment_Insurance_Individual失业金个人">Unemployment_Insurance_Individual失业金个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Unemployment_Individual, 2) %></span>

                    <span class="project" title="Housing_Fund_Individual公积金个人">Housing_Fund_Individual公积金个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Fund_Individual, 2) %></span>

                    <span class="project" title="个人所得税IIT">个人所得税IIT</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].iit, 2) %></span>

                    <span class="project" title="Pension_Company养老金公司">Pension_Company养老金公司</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Pension_Company, 2) %></span>

                    <span class="project" title="Work_related_Injury_Insurance工伤公司">Work_related_Injury_Insurance工伤公司</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Work_Related_Injury_Insurance, 2) %></span>

                    <span class="project" title="Maternity_Insurance生育险公司">Maternity_Insurance生育险公司</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Maternity_Insurance, 2) %></span>

                    <span class="project" title="Medical_Insurance_Company医保公司">Medical_Insurance_Company医保公司</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Medical_Insurance_Company, 2) %></span>

                    <span class="project" title="Unemployment_Insurance_Company失业金公司">Unemployment_Insurance_Company失业金公司</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Unemployment_Insurance_Company, 2) %></span>

                    <span class="project" title="Housing_Fund_Company公积金公司">Housing_Fund_Company公积金公司</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Fund_Company, 2) %></span>

                    <span class="project" title="iit_bonus年终奖个税">iit_bonus年终奖个税</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].iit_bonus, 2) %></span>

                    <span class="project" title="adjustment税前调整">Adjustment税前调整</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].adjustment, 2) %></span>

                </div>
            </div>
			
			<div class="cat">
                <div class="cat-title">Special Tax Exemption</div>
                <div class="cat-content">
                    <span class="project" title="Children_Education子女教育">Children_Education子女教育</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Children_Education, 2) %></span>

                    <span class="project" title="Diploma_Education继续教育 ">Diploma_Education继续教育 </span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Diploma_Education, 2) %></span>

                    <span class="project" title="Medical_Treatment_for_Serious_Illness大病医疗">Medical_Treatment_for_Serious_Illness大病医疗</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Medical_Treatment_for_Serious_Illness, 2) %></span>

                    <span class="project" title="Housing_Loan住房贷款">Housing_Loan住房贷款</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Loan, 2) %></span>

                    <span class="project" title="Housing_Rent住房租金">Housing_Rent住房租金</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Rent, 2) %></span>

                    <span class="project" title="Support_For_Elderly赡养老人">Support_For_Elderly赡养老人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Support_For_Elderly, 2) %></span>

                </div>
            </div>
            <div class="cat"></div>
        </div>
    </div>
    <hr/>
    <%}%>

</body>
</html>
