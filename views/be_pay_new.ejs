    <!DOCTYPE html>
<html>
<head>
    <title></title>
    <link rel='stylesheet' href='/stylesheets/style_pay.css' />
    <script src='/javascripts/jquery.min.js'></script>

</head>
<body>
<script type="text/javascript">
    
</script>
    <h1><%= name %> Salary Slip</h1>

    <% for(var i = 0; i < data.length; i++) { 
        function format(num, cent) {
            if (num != null) {
                return (num.toFixed(cent) + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
            } else {
                return num;
            }
        }
    %>
    <div class="scheme">
        <div class="scheme-title">
            <h3><%= name %></h3>
            <span class="title">Salary Slip</span> -
            <span class="title"><%= data[i].income_year + '-' + data[i].income_month %></span>
        </div>
        <div class="scheme-content">
            <div class="cat" style="border-top: 0;">
                <div class="cat-title">Payable Items</div>
                <div class="cat-content">
                    <span class="project" title="Contract_Salary/合同工资">Contract_Salary合同工资</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Contract_Salary合同工资, 2) %></span>

                    <span class="project" title="Performance Salary/绩效工资">Performance Salary绩效工资</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Performance_Salary, 2) %></span>

                    <span class="project" title="Commission/业绩佣金">Commission业绩佣金</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Commission业绩奖金, 2) %></span>

                    <span class="project" title="Bonus/奖金">Bonus奖金</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Bonus奖金, 2) %></span>

                    <span class="project" title="Allowance/补贴">Allowance补贴</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Allowance补贴, 2) %></span>

                    <span class="project" title="Position_Subsidy岗位津贴">Position_Subsidy岗位津贴</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Position_Subsidy岗位津贴, 2) %></span>
                </div>
            </div>
            <div class="cat" style="border-top: 0;">
            <div class="cat-title">Special Tax Exceptions</div>
            <div class="cat-content">
                <span class="project" title="Children_Education子女教育">Children_Education子女教育</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Children_Education子女教育, 2) %></span>

                <span class="project" title="Diploma_Education继续教育">Diploma_Education继续教育</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Diploma_Education继续教育, 2) %></span>

                <span class="project" title="Housing_Load住房贷款">Housing_Load住房贷款</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Loan住房贷款, 2) %></span>

                <span class="project" title="Housing_Rent住房租金">Housing_Rent住房租金</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Rent住房租金, 2) %></span>

                <span class="project" title="Support_for_Elderly赡养老人">Support_for_Elderly赡养老人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Support_For_Elderly赡养老人, 2) %></span>

                <span class="project" title="Medical_Treatment_for_Serious_Illness大病医疗">Medical_Treatment_for_Serious_Illness大病医疗</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Medical_Treatment_for_Serious_Illness大病医疗, 2) %></span>
            </div>
        </div>

        <div class="cat">
            <div class="cat-title">Deduct Items</div>
            <div class="cat-content">
                <span class="project" title="Personal_Leave_Deduction事假扣款">Personal_Leave_Deduction事假扣款</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Personal_Leave_Deduction事假扣款, 2) %></span>

                <span class="project" title="Sick_Leave_Deduction病假扣款">Sick_Leave_Deduction病假扣款</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Sickleave_Deduction病假扣款, 2) %></span>

                <span class="project" title="Pension_Individual养老金个人">Pension_Individual养老金个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Pension_Individual养老金个人, 2) %></span>

                <span class="project" title="Medical_Insurance_Individual医保个人">Medical_Insurance_Individual医保个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Medical_Individual医保个人, 2) %></span>

                <span class="project" title="Unemployment_Insurance_Individual失业金个人">Unemployment_Insurance_Individual失业金个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Unemployment_Individual失业金个人, 2) %></span>

                <span class="project" title="Housing_Fund_Individual公积金个人">Housing_Fund_Individual公积金个人</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Housing_Fund_Individual公积金个人, 2) %></span>

                <span class="project" title="Before_Tax_Deduction税前扣款">Before_Tax_Deduction税前扣款</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Before_Tax_Deduction税前扣款, 2) %></span>

                <span class="project" style="color:red;font-style:italic" title="Before_Tax_Deduction税前扣款">“*-deduction” means “add”<br>“*-扣除”意味着“增加”</span>
            </div>
        </div>

        <div class="cat">
            <div class="cat-title">Income</div>
            <div class="cat-content">
                <span class="project" title="Taxable_Salary当月应纳税所得额">Taxable_Salary当月应纳税所得额</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Taxable_Salary当月应纳税所得额, 2) %></span>

                <span class="project" title="After_Tax_Adjustment税后调整项 ">After_Tax_Adjustment税后调整项 </span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].After_Tax_Adjustment税后调整项, 2) %></span>

                <span class="project" title="Tax税额">Tax税额</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Tax税额, 2) %></span>

                <span class="project" title="RedPack">RedPack</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Red_Pack, 2) %></span>

                <span class="project" title="Net_Income当月净收入">Net_Income当月净收入</span><span class="pay" style="color:green;text-align:right;"><%= format(data[i].Net_Income当月净收入, 2) %></span>
            </div>
        </div>
        <div class="cat"></div>
        </div>
    </div>
    <hr/>
    <%}%>

</body>
</html>
